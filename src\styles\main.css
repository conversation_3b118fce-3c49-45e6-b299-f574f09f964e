* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    overflow: hidden;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem 2rem;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #64b3f4 0%, #c2e59c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧控制面板 */
.control-panel {
    width: 300px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.control-panel::-webkit-scrollbar {
    width: 6px;
}

.control-panel::-webkit-scrollbar-track {
    background: transparent;
}

.control-panel::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.panel-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-section:last-child {
    border-bottom: none;
}

.panel-section h3 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: #64b3f4;
}

/* 控件样式 */
.control-select,
.control-input {
    width: 100%;
    padding: 0.7rem;
    margin-bottom: 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.control-select:focus,
.control-input:focus {
    outline: none;
    border-color: #64b3f4;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px rgba(100, 179, 244, 0.2);
}

.control-select option {
    background: #2a2a2a;
    color: #ffffff;
}

.input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
}

.input-group .control-input {
    flex: 1;
    margin-bottom: 0;
}

.control-btn {
    padding: 0.7rem 1.2rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-btn.primary {
    background: linear-gradient(45deg, #64b3f4, #4a90e2);
    color: white;
}

.control-btn.primary:hover {
    background: linear-gradient(45deg, #5aa3e4, #3a80d2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(100, 179, 244, 0.3);
}

.control-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.control-btn.danger {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
}

.control-btn.danger:hover {
    background: linear-gradient(45deg, #ff5b5b, #de4a42);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.control-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 速度控制 */
.speed-control {
    margin-bottom: 1rem;
}

.speed-control label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    appearance: none;
}

.slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #64b3f4;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #64b3f4;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* 状态信息 */
.status-info p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.status-info span {
    color: #64b3f4;
    font-weight: 500;
}

/* 3D可视化区域 */
.visualization-area {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#threeCanvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* 加载覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid #64b3f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 底部信息栏 */
.footer {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem 2rem;
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    max-height: 120px;
    overflow-y: auto;
}

.algorithm-info h4 {
    color: #64b3f4;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.algorithm-info p {
    font-size: 0.9rem;
    line-height: 1.4;
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .control-panel {
        width: 250px;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .control-panel {
        width: 100%;
        max-height: 300px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .visualization-area {
        height: 400px;
    }
    
    .footer {
        display: none;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(100, 179, 244, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(100, 179, 244, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(100, 179, 244, 0);
    }
}
