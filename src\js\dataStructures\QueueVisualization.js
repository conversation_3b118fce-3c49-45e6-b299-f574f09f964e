import * as THREE from 'three';
import { gsap } from 'gsap';
import { BaseDataStructure } from './BaseDataStructure.js';

export class QueueVisualization extends BaseDataStructure {
    constructor(scene) {
        super(scene);
        this.spacing = 1.5;
        this.maxElements = 12;
        this.frontIndex = 0;
        this.rearIndex = -1;
    }
    
    init() {
        super.init();
        this.createQueueBase();
        this.createQueuePointers();
    }
    
    createQueueBase() {
        // 创建队列基础平台
        const baseGeometry = new THREE.PlaneGeometry(this.maxElements * this.spacing + 2, 2);
        const baseMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            transparent: true,
            opacity: 0.3
        });
        
        this.baseMesh = new THREE.Mesh(baseGeometry, baseMaterial);
        this.baseMesh.rotation.x = -Math.PI / 2;
        this.baseMesh.position.y = -1;
        this.baseMesh.receiveShadow = true;
        
        this.scene.add(this.baseMesh);
        
        // 创建队列标识
        this.createQueueLabel();
    }
    
    createQueueLabel() {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;
        
        context.fillStyle = '#64b3f4';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = '#ffffff';
        context.font = 'bold 32px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText('QUEUE', canvas.width / 2, canvas.height / 2);
        
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);
        
        sprite.scale.set(2, 1, 1);
        sprite.position.set(0, -2.5, 0);
        
        this.scene.add(sprite);
        this.labelSprite = sprite;
    }
    
    createQueuePointers() {
        // 前端指针 (Front)
        this.createFrontPointer();
        
        // 后端指针 (Rear)
        this.createRearPointer();
    }
    
    createFrontPointer() {
        if (this.frontPointer) {
            this.scene.remove(this.frontPointer);
            this.scene.remove(this.frontLabel);
        }
        
        if (this.elements.length > 0) {
            const frontPosition = this.getElementPosition(0);
            
            // 前端箭头
            const arrowGeometry = new THREE.ConeGeometry(0.2, 0.6, 8);
            const arrowMaterial = new THREE.MeshLambertMaterial({ color: 0x4CAF50 });
            this.frontPointer = new THREE.Mesh(arrowGeometry, arrowMaterial);
            
            this.frontPointer.position.set(frontPosition.x, frontPosition.y + 2, frontPosition.z);
            this.frontPointer.rotation.x = Math.PI;
            
            this.scene.add(this.frontPointer);
            
            // FRONT 标签
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 128;
            canvas.height = 64;
            
            context.fillStyle = '#4CAF50';
            context.fillRect(0, 0, canvas.width, canvas.height);
            
            context.fillStyle = '#ffffff';
            context.font = 'bold 20px Arial';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillText('FRONT', canvas.width / 2, canvas.height / 2);
            
            const texture = new THREE.CanvasTexture(canvas);
            const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
            this.frontLabel = new THREE.Sprite(spriteMaterial);
            
            this.frontLabel.scale.set(1, 0.5, 1);
            this.frontLabel.position.set(frontPosition.x, frontPosition.y + 3, frontPosition.z);
            
            this.scene.add(this.frontLabel);
        }
    }
    
    createRearPointer() {
        if (this.rearPointer) {
            this.scene.remove(this.rearPointer);
            this.scene.remove(this.rearLabel);
        }
        
        if (this.elements.length > 0) {
            const rearPosition = this.getElementPosition(this.elements.length - 1);
            
            // 后端箭头
            const arrowGeometry = new THREE.ConeGeometry(0.2, 0.6, 8);
            const arrowMaterial = new THREE.MeshLambertMaterial({ color: 0xFF5722 });
            this.rearPointer = new THREE.Mesh(arrowGeometry, arrowMaterial);
            
            this.rearPointer.position.set(rearPosition.x, rearPosition.y - 2, rearPosition.z);
            
            this.scene.add(this.rearPointer);
            
            // REAR 标签
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 128;
            canvas.height = 64;
            
            context.fillStyle = '#FF5722';
            context.fillRect(0, 0, canvas.width, canvas.height);
            
            context.fillStyle = '#ffffff';
            context.font = 'bold 20px Arial';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillText('REAR', canvas.width / 2, canvas.height / 2);
            
            const texture = new THREE.CanvasTexture(canvas);
            const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
            this.rearLabel = new THREE.Sprite(spriteMaterial);
            
            this.rearLabel.scale.set(1, 0.5, 1);
            this.rearLabel.position.set(rearPosition.x, rearPosition.y - 3, rearPosition.z);
            
            this.scene.add(this.rearLabel);
        }
    }
    
    addElement(value) {
        if (this.elements.length >= this.maxElements) {
            console.warn('Queue is full');
            return;
        }
        
        this.elements.push(value);
        this.rearIndex++;
        
        const index = this.elements.length - 1;
        const position = this.getElementPosition(index);
        
        // 创建网格
        const mesh = this.createElementMesh(value, position);
        
        this.scene.add(mesh);
        this.meshes.push(mesh);
        
        // 入队动画：从右侧滑入
        gsap.from(mesh.position, {
            duration: 0.6,
            x: position.x + 3,
            ease: 'power2.out'
        });
        
        gsap.from(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.out(1.7)'
        });
        
        // 更新指针
        this.updatePointers();
    }
    
    removeElement() {
        if (this.elements.length === 0) {
            console.warn('Queue is empty');
            return;
        }
        
        const mesh = this.meshes[0];
        
        // 出队动画：向左侧滑出
        gsap.to(mesh.position, {
            duration: 0.5,
            x: mesh.position.x - 3,
            ease: 'power2.in'
        });
        
        gsap.to(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.in(1.7)',
            onComplete: () => {
                this.scene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            }
        });
        
        // 移除元素
        this.elements.shift();
        this.meshes.shift();
        this.frontIndex++;
        
        // 重新排列剩余元素
        this.rearrangeElements();
        
        // 更新指针
        this.updatePointers();
    }
    
    rearrangeElements() {
        this.meshes.forEach((mesh, index) => {
            const newPosition = this.getElementPosition(index);
            
            gsap.to(mesh.position, {
                duration: 0.5,
                x: newPosition.x,
                y: newPosition.y,
                z: newPosition.z,
                ease: 'power2.out'
            });
        });
    }
    
    getElementPosition(index) {
        const startX = -(this.elements.length - 1) * this.spacing / 2;
        return {
            x: startX + index * this.spacing,
            y: 0,
            z: 0
        };
    }
    
    updatePointers() {
        this.createFrontPointer();
        this.createRearPointer();
    }
    
    // 入队操作（别名）
    enqueue(value) {
        this.addElement(value);
    }
    
    // 出队操作（别名）
    dequeue() {
        const value = this.elements.length > 0 ? this.elements[0] : null;
        this.removeElement();
        return value;
    }
    
    // 查看队首元素
    front() {
        return this.elements.length > 0 ? this.elements[0] : null;
    }
    
    // 查看队尾元素
    rear() {
        return this.elements.length > 0 ? this.elements[this.elements.length - 1] : null;
    }
    
    // 检查队列是否为空
    isEmpty() {
        return this.elements.length === 0;
    }
    
    // 高亮队首元素
    highlightFront(color = 0xffff00) {
        if (this.meshes.length > 0) {
            const frontMesh = this.meshes[0];
            frontMesh.material.color.setHex(color);
            frontMesh.material.emissive.setHex(color * 0.1);
        }
    }
    
    // 高亮队尾元素
    highlightRear(color = 0xffff00) {
        if (this.meshes.length > 0) {
            const rearMesh = this.meshes[this.meshes.length - 1];
            rearMesh.material.color.setHex(color);
            rearMesh.material.emissive.setHex(color * 0.1);
        }
    }
    
    // 重置所有颜色
    resetAllColors() {
        this.meshes.forEach(mesh => {
            mesh.material.color.setHex(0x64b3f4);
            mesh.material.emissive.setHex(0x000000);
        });
    }
    
    clear() {
        super.clear();
        
        // 清除指针
        if (this.frontPointer) {
            this.scene.remove(this.frontPointer);
            this.frontPointer = null;
        }
        if (this.frontLabel) {
            this.scene.remove(this.frontLabel);
            this.frontLabel = null;
        }
        if (this.rearPointer) {
            this.scene.remove(this.rearPointer);
            this.rearPointer = null;
        }
        if (this.rearLabel) {
            this.scene.remove(this.rearLabel);
            this.rearLabel = null;
        }
        
        this.frontIndex = 0;
        this.rearIndex = -1;
    }
    
    dispose() {
        this.clear();
        
        if (this.baseMesh) {
            this.scene.remove(this.baseMesh);
            this.baseMesh.geometry.dispose();
            this.baseMesh.material.dispose();
        }
        
        if (this.labelSprite) {
            this.scene.remove(this.labelSprite);
        }
    }
}
