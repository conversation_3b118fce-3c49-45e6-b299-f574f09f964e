import { BaseAlgorithm } from './BaseAlgorithm.js';

export class BFSAlgorithm extends BaseAlgorithm {
    constructor() {
        super();
        this.name = 'Breadth-First Search';
        this.description = '广度优先搜索：逐层访问节点，使用队列实现';
    }
    
    async run(dataStructure) {
        const nodes = dataStructure.getAllNodes();
        if (!nodes || nodes.length === 0) {
            return;
        }
        
        // 重置所有节点颜色
        dataStructure.resetAllColors();
        
        // 选择起始节点（第一个节点）
        const startNode = nodes[0];
        const visited = new Set();
        const queue = [startNode];
        
        // 高亮起始节点
        dataStructure.highlightNode(startNode, 0x4CAF50); // 绿色表示起始节点
        
        await this.delay(1000);
        
        while (queue.length > 0) {
            // 检查是否暂停
            await this.waitForResume();
            if (!this.isRunning) return;
            
            // 从队列中取出一个节点
            const currentNode = queue.shift();
            
            if (visited.has(currentNode)) {
                continue;
            }
            
            // 标记为已访问
            visited.add(currentNode);
            
            // 高亮当前访问的节点
            dataStructure.highlightNode(currentNode, 0xFF9800); // 橙色表示当前节点
            
            await this.delay(1000);
            
            // 获取邻居节点
            let neighbors = [];
            
            if (dataStructure.getNeighbors) {
                // 图结构
                neighbors = dataStructure.getNeighbors(currentNode);
            } else if (dataStructure.getAllNodes) {
                // 树结构 - 简化逻辑：相邻的值作为邻居
                const allNodes = dataStructure.getAllNodes();
                const currentIndex = allNodes.indexOf(currentNode);
                
                // 左子节点
                if (currentIndex * 2 + 1 < allNodes.length) {
                    neighbors.push(allNodes[currentIndex * 2 + 1]);
                }
                
                // 右子节点
                if (currentIndex * 2 + 2 < allNodes.length) {
                    neighbors.push(allNodes[currentIndex * 2 + 2]);
                }
            }
            
            // 访问邻居节点
            for (const neighbor of neighbors) {
                if (!visited.has(neighbor) && !queue.includes(neighbor)) {
                    queue.push(neighbor);
                    
                    // 高亮即将访问的邻居
                    dataStructure.highlightNode(neighbor, 0xFFEB3B); // 黄色表示在队列中
                    
                    await this.delay(500);
                }
            }
            
            // 将当前节点标记为已完成访问
            dataStructure.highlightNode(currentNode, 0x2196F3); // 蓝色表示已访问
            
            await this.delay(800);
        }
        
        // 最终效果：所有已访问的节点变为绿色
        await this.delay(1000);
        visited.forEach(node => {
            dataStructure.highlightNode(node, 0x4CAF50);
        });
        
        await this.delay(2000);
        dataStructure.resetAllColors();
    }
    
    getComplexity() {
        return {
            time: 'O(V + E)',
            space: 'O(V)',
            description: 'V是顶点数，E是边数'
        };
    }
}
