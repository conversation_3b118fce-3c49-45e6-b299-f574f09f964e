import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { gsap } from 'gsap';

// 导入数据结构类
import { ArrayVisualization } from './dataStructures/ArrayVisualization.js';
import { LinkedListVisualization } from './dataStructures/LinkedListVisualization.js';
import { StackVisualization } from './dataStructures/StackVisualization.js';
import { QueueVisualization } from './dataStructures/QueueVisualization.js';
import { BinaryTreeVisualization } from './dataStructures/BinaryTreeVisualization.js';
import { GraphVisualization } from './dataStructures/GraphVisualization.js';

// 导入算法类
import { BubbleSortAlgorithm } from './algorithms/BubbleSortAlgorithm.js';
import { QuickSortAlgorithm } from './algorithms/QuickSortAlgorithm.js';
import { BFSAlgorithm } from './algorithms/BFSAlgorithm.js';
import { DFSAlgorithm } from './algorithms/DFSAlgorithm.js';
import { BinarySearchAlgorithm } from './algorithms/BinarySearchAlgorithm.js';

class DataStructureInsightLens {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.gui = null;
        
        this.currentStructure = null;
        this.currentAlgorithm = null;
        this.animationSpeed = 1;
        this.isAlgorithmRunning = false;
        
        this.dataStructures = {};
        this.algorithms = {};
        
        this.init();
        this.setupEventListeners();
        this.animate();
    }
    
    init() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0f1419);
        
        // 设置相机
        const canvas = document.getElementById('threeCanvas');
        this.camera = new THREE.PerspectiveCamera(
            75,
            canvas.clientWidth / canvas.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 5, 10);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 设置控制器
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.maxDistance = 50;
        this.controls.minDistance = 2;
        
        // 添加光照
        this.setupLighting();
        
        // 初始化数据结构
        this.initDataStructures();
        
        // 初始化算法
        this.initAlgorithms();
        
        // 设置默认数据结构
        this.switchStructure('array');
        
        // 隐藏加载覆盖层
        this.hideLoading();
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // 主方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -20;
        directionalLight.shadow.camera.right = 20;
        directionalLight.shadow.camera.top = 20;
        directionalLight.shadow.camera.bottom = -20;
        this.scene.add(directionalLight);
        
        // 补充光
        const fillLight = new THREE.DirectionalLight(0x64b3f4, 0.3);
        fillLight.position.set(-5, 3, -5);
        this.scene.add(fillLight);
        
        // 点光源（用于增强效果）
        const pointLight = new THREE.PointLight(0xffffff, 0.5, 20);
        pointLight.position.set(0, 8, 0);
        this.scene.add(pointLight);
    }
    
    initDataStructures() {
        this.dataStructures = {
            array: new ArrayVisualization(this.scene),
            linkedlist: new LinkedListVisualization(this.scene),
            stack: new StackVisualization(this.scene),
            queue: new QueueVisualization(this.scene),
            binaryTree: new BinaryTreeVisualization(this.scene),
            graph: new GraphVisualization(this.scene)
        };
    }
    
    initAlgorithms() {
        this.algorithms = {
            bubbleSort: new BubbleSortAlgorithm(),
            quickSort: new QuickSortAlgorithm(),
            bfs: new BFSAlgorithm(),
            dfs: new DFSAlgorithm(),
            binarySearch: new BinarySearchAlgorithm()
        };
    }
    
    setupEventListeners() {
        // 数据结构选择
        document.getElementById('structureSelect').addEventListener('change', (e) => {
            this.switchStructure(e.target.value);
        });
        
        // 数据操作
        document.getElementById('addBtn').addEventListener('click', () => {
            const value = parseInt(document.getElementById('valueInput').value);
            if (!isNaN(value)) {
                this.addElement(value);
                document.getElementById('valueInput').value = '';
            }
        });
        
        document.getElementById('removeBtn').addEventListener('click', () => {
            const index = parseInt(document.getElementById('indexInput').value);
            this.removeElement(index);
            document.getElementById('indexInput').value = '';
        });
        
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearStructure();
        });
        
        // 算法控制
        document.getElementById('algorithmSelect').addEventListener('change', (e) => {
            this.updateAlgorithmDescription(e.target.value);
        });
        
        document.getElementById('startAlgorithm').addEventListener('click', () => {
            this.startAlgorithm();
        });
        
        document.getElementById('pauseAlgorithm').addEventListener('click', () => {
            this.pauseAlgorithm();
        });
        
        document.getElementById('resetAlgorithm').addEventListener('click', () => {
            this.resetAlgorithm();
        });
        
        // 视图控制
        document.getElementById('speedSlider').addEventListener('input', (e) => {
            this.animationSpeed = parseFloat(e.target.value);
            document.getElementById('speedValue').textContent = `${this.animationSpeed}x`;
        });
        
        document.getElementById('resetCamera').addEventListener('click', () => {
            this.resetCamera();
        });
        
        // 窗口调整
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }
    
    switchStructure(structureType) {
        if (this.currentStructure) {
            this.currentStructure.dispose();
        }
        
        this.currentStructure = this.dataStructures[structureType];
        this.currentStructure.init();
        
        // 更新UI
        const structureNames = {
            array: '数组',
            linkedlist: '链表',
            stack: '栈',
            queue: '队列',
            binaryTree: '二叉树',
            graph: '图'
        };
        
        document.getElementById('currentStructure').textContent = structureNames[structureType];
        this.updateElementCount();
        
        // 更新可用算法
        this.updateAvailableAlgorithms(structureType);
        
        // 重置相机位置
        this.resetCamera();
    }
    
    updateAvailableAlgorithms(structureType) {
        const algorithmSelect = document.getElementById('algorithmSelect');
        algorithmSelect.innerHTML = '<option value="">选择算法</option>';
        
        const algorithmsByStructure = {
            array: [
                { value: 'bubbleSort', text: '冒泡排序' },
                { value: 'quickSort', text: '快速排序' },
                { value: 'binarySearch', text: '二分查找' }
            ],
            linkedlist: [
                { value: 'bubbleSort', text: '冒泡排序' }
            ],
            stack: [],
            queue: [],
            binaryTree: [
                { value: 'bfs', text: '广度优先搜索' },
                { value: 'dfs', text: '深度优先搜索' }
            ],
            graph: [
                { value: 'bfs', text: '广度优先搜索' },
                { value: 'dfs', text: '深度优先搜索' }
            ]
        };
        
        const algorithms = algorithmsByStructure[structureType] || [];
        algorithms.forEach(alg => {
            const option = document.createElement('option');
            option.value = alg.value;
            option.textContent = alg.text;
            algorithmSelect.appendChild(option);
        });
    }
    
    addElement(value) {
        if (this.currentStructure) {
            this.currentStructure.addElement(value);
            this.updateElementCount();
        }
    }
    
    removeElement(index) {
        if (this.currentStructure) {
            this.currentStructure.removeElement(index);
            this.updateElementCount();
        }
    }
    
    clearStructure() {
        if (this.currentStructure) {
            this.currentStructure.clear();
            this.updateElementCount();
        }
    }
    
    updateElementCount() {
        if (this.currentStructure) {
            const count = this.currentStructure.getElementCount();
            document.getElementById('elementCount').textContent = count;
        }
    }
    
    startAlgorithm() {
        const algorithmType = document.getElementById('algorithmSelect').value;
        if (!algorithmType || !this.currentStructure) return;
        
        const algorithm = this.algorithms[algorithmType];
        if (algorithm) {
            this.isAlgorithmRunning = true;
            this.updateAlgorithmStatus('运行中');
            
            algorithm.execute(this.currentStructure, this.animationSpeed)
                .then(() => {
                    this.isAlgorithmRunning = false;
                    this.updateAlgorithmStatus('完成');
                })
                .catch((error) => {
                    console.error('Algorithm execution error:', error);
                    this.isAlgorithmRunning = false;
                    this.updateAlgorithmStatus('错误');
                });
        }
    }
    
    pauseAlgorithm() {
        if (this.currentAlgorithm) {
            this.currentAlgorithm.pause();
            this.updateAlgorithmStatus('暂停');
        }
    }
    
    resetAlgorithm() {
        if (this.currentAlgorithm) {
            this.currentAlgorithm.reset();
        }
        this.isAlgorithmRunning = false;
        this.updateAlgorithmStatus('就绪');
    }
    
    updateAlgorithmStatus(status) {
        document.getElementById('algorithmStatus').textContent = status;
    }
    
    updateAlgorithmDescription(algorithmType) {
        const descriptions = {
            bubbleSort: '冒泡排序：通过重复遍历数组，比较相邻元素并交换位置，将较大的元素"冒泡"到数组末尾。时间复杂度O(n²)。',
            quickSort: '快速排序：选择一个基准元素，将数组分为小于和大于基准的两部分，然后递归排序。平均时间复杂度O(n log n)。',
            bfs: '广度优先搜索：从起始节点开始，逐层访问所有相邻节点，使用队列数据结构实现。适用于寻找最短路径。',
            dfs: '深度优先搜索：从起始节点开始，沿着一条路径尽可能深入，然后回溯。使用栈数据结构或递归实现。',
            binarySearch: '二分查找：在有序数组中查找目标值，每次将搜索范围缩小一半。时间复杂度O(log n)。'
        };
        
        const description = descriptions[algorithmType] || '选择一个算法查看详细说明';
        document.getElementById('algorithmDescription').textContent = description;
    }
    
    resetCamera() {
        gsap.to(this.camera.position, {
            duration: 1,
            x: 0,
            y: 5,
            z: 10,
            ease: 'power2.out'
        });
        
        gsap.to(this.controls.target, {
            duration: 1,
            x: 0,
            y: 0,
            z: 0,
            ease: 'power2.out'
        });
    }
    
    handleKeyboard(event) {
        switch(event.code) {
            case 'Space':
                event.preventDefault();
                if (this.isAlgorithmRunning) {
                    this.pauseAlgorithm();
                } else {
                    this.startAlgorithm();
                }
                break;
            case 'KeyR':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.resetAlgorithm();
                }
                break;
            case 'KeyC':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.clearStructure();
                }
                break;
        }
    }
    
    onWindowResize() {
        const canvas = document.getElementById('threeCanvas');
        this.camera.aspect = canvas.clientWidth / canvas.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    }
    
    showLoading() {
        document.getElementById('loadingOverlay').classList.add('show');
    }
    
    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('show');
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        this.controls.update();
        
        // 更新当前数据结构
        if (this.currentStructure) {
            this.currentStructure.update();
        }
        
        // 更新当前算法
        if (this.currentAlgorithm) {
            this.currentAlgorithm.update();
        }
        
        this.renderer.render(this.scene, this.camera);
    }
}

// 启动应用
document.addEventListener('DOMContentLoaded', () => {
    new DataStructureInsightLens();
});
