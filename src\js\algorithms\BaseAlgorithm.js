export class BaseAlgorithm {
    constructor() {
        this.isRunning = false;
        this.isPaused = false;
        this.speed = 1;
        this.currentStep = 0;
        this.steps = [];
    }
    
    async execute(dataStructure, speed = 1) {
        this.speed = speed;
        this.isRunning = true;
        this.isPaused = false;
        this.currentStep = 0;
        
        try {
            await this.run(dataStructure);
        } catch (error) {
            console.error('Algorithm execution error:', error);
            throw error;
        } finally {
            this.isRunning = false;
        }
    }
    
    async run(dataStructure) {
        throw new Error('run method must be implemented');
    }
    
    pause() {
        this.isPaused = true;
    }
    
    resume() {
        this.isPaused = false;
    }
    
    reset() {
        this.isRunning = false;
        this.isPaused = false;
        this.currentStep = 0;
        this.steps = [];
    }
    
    async delay(ms) {
        const adjustedMs = ms / this.speed;
        return new Promise(resolve => {
            const checkPause = () => {
                if (!this.isPaused) {
                    setTimeout(resolve, adjustedMs);
                } else {
                    setTimeout(checkPause, 100);
                }
            };
            checkPause();
        });
    }
    
    async waitForResume() {
        while (this.isPaused && this.isRunning) {
            await this.delay(100);
        }
    }
}
