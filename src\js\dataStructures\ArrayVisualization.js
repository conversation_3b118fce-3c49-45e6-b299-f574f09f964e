import * as THREE from 'three';
import { gsap } from 'gsap';
import { BaseDataStructure } from './BaseDataStructure.js';

export class ArrayVisualization extends BaseDataStructure {
    constructor(scene) {
        super(scene);
        this.spacing = 1.5; // 元素间距
        this.maxElements = 20; // 最大元素数量
    }
    
    init() {
        super.init();
        // 创建数组基础平台
        this.createArrayBase();
    }
    
    createArrayBase() {
        // 创建数组基础平台
        const baseGeometry = new THREE.PlaneGeometry(this.maxElements * this.spacing + 2, 2);
        const baseMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            transparent: true,
            opacity: 0.3
        });
        
        this.baseMesh = new THREE.Mesh(baseGeometry, baseMaterial);
        this.baseMesh.rotation.x = -Math.PI / 2;
        this.baseMesh.position.y = -1;
        this.baseMesh.receiveShadow = true;
        
        this.scene.add(this.baseMesh);
    }
    
    addElement(value) {
        if (this.elements.length >= this.maxElements) {
            console.warn('Array is full');
            return;
        }
        
        this.elements.push(value);
        const index = this.elements.length - 1;
        
        // 计算位置
        const position = this.getElementPosition(index);
        
        // 创建网格
        const mesh = this.createElementMesh(value, { x: position.x, y: position.y + 2, z: position.z });
        
        // 添加到场景
        this.scene.add(mesh);
        this.meshes.push(mesh);
        
        // 动画效果：从上方落下
        gsap.from(mesh.position, {
            duration: 0.6,
            y: position.y + 5,
            ease: 'bounce.out'
        });
        
        gsap.from(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.out(1.7)'
        });
        
        // 创建索引标签
        this.createIndexLabel(index, position);
    }
    
    removeElement(index) {
        if (index < 0 || index >= this.elements.length) {
            console.warn('Invalid index');
            return;
        }
        
        const mesh = this.meshes[index];
        
        // 动画效果：缩小并消失
        gsap.to(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.in(1.7)',
            onComplete: () => {
                this.scene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            }
        });
        
        // 移除元素和网格
        this.elements.splice(index, 1);
        this.meshes.splice(index, 1);
        
        // 重新排列剩余元素
        this.rearrangeElements();
        
        // 更新索引标签
        this.updateIndexLabels();
    }
    
    rearrangeElements() {
        this.meshes.forEach((mesh, index) => {
            const newPosition = this.getElementPosition(index);
            
            gsap.to(mesh.position, {
                duration: 0.5,
                x: newPosition.x,
                y: newPosition.y,
                z: newPosition.z,
                ease: 'power2.out'
            });
        });
    }
    
    getElementPosition(index) {
        const startX = -(this.elements.length - 1) * this.spacing / 2;
        return {
            x: startX + index * this.spacing,
            y: 0,
            z: 0
        };
    }
    
    createIndexLabel(index, position) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 64;
        
        context.fillStyle = '#64b3f4';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = '#ffffff';
        context.font = 'bold 24px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(`[${index}]`, canvas.width / 2, canvas.height / 2);
        
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);
        
        sprite.scale.set(0.8, 0.4, 1);
        sprite.position.set(position.x, position.y - 1.5, position.z);
        
        this.scene.add(sprite);
        
        // 存储索引标签以便后续更新
        if (!this.indexLabels) this.indexLabels = [];
        this.indexLabels[index] = sprite;
    }
    
    updateIndexLabels() {
        // 清除旧的索引标签
        if (this.indexLabels) {
            this.indexLabels.forEach(label => {
                if (label) this.scene.remove(label);
            });
        }
        this.indexLabels = [];
        
        // 创建新的索引标签
        this.elements.forEach((_, index) => {
            const position = this.getElementPosition(index);
            this.createIndexLabel(index, position);
        });
    }
    
    clear() {
        super.clear();
        
        // 清除索引标签
        if (this.indexLabels) {
            this.indexLabels.forEach(label => {
                if (label) this.scene.remove(label);
            });
            this.indexLabels = [];
        }
        
        // 移除基础平台
        if (this.baseMesh) {
            this.scene.remove(this.baseMesh);
            this.baseMesh.geometry.dispose();
            this.baseMesh.material.dispose();
            this.baseMesh = null;
        }
    }
    
    // 获取指定索引的元素（用于算法）
    getElementAt(index) {
        return this.elements[index];
    }
    
    // 获取指定索引的网格（用于算法动画）
    getMeshAt(index) {
        return this.meshes[index];
    }
    
    // 交换两个元素（用于排序算法）
    async swapElements(index1, index2) {
        if (index1 < 0 || index1 >= this.elements.length || 
            index2 < 0 || index2 >= this.elements.length) {
            return;
        }
        
        // 交换数据
        [this.elements[index1], this.elements[index2]] = [this.elements[index2], this.elements[index1]];
        
        // 获取网格
        const mesh1 = this.meshes[index1];
        const mesh2 = this.meshes[index2];
        
        // 获取位置
        const pos1 = this.getElementPosition(index1);
        const pos2 = this.getElementPosition(index2);
        
        // 创建交换动画
        const duration = 0.8;
        
        return new Promise((resolve) => {
            // 高亮显示
            this.highlightElement(index1, 0xff6b6b);
            this.highlightElement(index2, 0xff6b6b);
            
            // 向上移动
            gsap.to([mesh1.position, mesh2.position], {
                duration: duration / 3,
                y: pos1.y + 1.5,
                ease: 'power2.out',
                onComplete: () => {
                    // 交换位置
                    gsap.to(mesh1.position, {
                        duration: duration / 3,
                        x: pos2.x,
                        ease: 'power2.inOut'
                    });
                    
                    gsap.to(mesh2.position, {
                        duration: duration / 3,
                        x: pos1.x,
                        ease: 'power2.inOut',
                        onComplete: () => {
                            // 向下移动
                            gsap.to([mesh1.position, mesh2.position], {
                                duration: duration / 3,
                                y: pos1.y,
                                ease: 'power2.in',
                                onComplete: () => {
                                    // 交换网格数组中的位置
                                    [this.meshes[index1], this.meshes[index2]] = [this.meshes[index2], this.meshes[index1]];
                                    
                                    // 恢复颜色
                                    this.resetElementColor(index1);
                                    this.resetElementColor(index2);
                                    
                                    resolve();
                                }
                            });
                        }
                    });
                }
            });
        });
    }
    
    // 高亮元素
    highlightElement(index, color = 0xffff00) {
        if (index >= 0 && index < this.meshes.length) {
            const mesh = this.meshes[index];
            mesh.material.color.setHex(color);
            mesh.material.emissive.setHex(color * 0.1);
        }
    }
    
    // 重置元素颜色
    resetElementColor(index) {
        if (index >= 0 && index < this.meshes.length) {
            const mesh = this.meshes[index];
            mesh.material.color.setHex(0x64b3f4);
            mesh.material.emissive.setHex(0x000000);
        }
    }
    
    // 重置所有元素颜色
    resetAllColors() {
        this.meshes.forEach((_, index) => {
            this.resetElementColor(index);
        });
    }
    
    dispose() {
        this.clear();
        if (this.baseMesh) {
            this.scene.remove(this.baseMesh);
            this.baseMesh.geometry.dispose();
            this.baseMesh.material.dispose();
        }
    }
}
