import { BaseAlgorithm } from './BaseAlgorithm.js';

export class BubbleSortAlgorithm extends BaseAlgorithm {
    constructor() {
        super();
        this.name = 'Bubble Sort';
        this.description = '冒泡排序：通过重复遍历数组，比较相邻元素并交换位置';
    }
    
    async run(dataStructure) {
        if (!dataStructure.elements || dataStructure.elements.length <= 1) {
            return;
        }
        
        const n = dataStructure.elements.length;
        let swapped;
        
        // 重置所有元素颜色
        dataStructure.resetAllColors();
        
        for (let i = 0; i < n - 1; i++) {
            swapped = false;
            
            for (let j = 0; j < n - i - 1; j++) {
                // 检查是否暂停
                await this.waitForResume();
                if (!this.isRunning) return;
                
                // 高亮当前比较的元素
                dataStructure.highlightElement(j, 0xffff00);     // 黄色
                dataStructure.highlightElement(j + 1, 0xffff00); // 黄色
                
                await this.delay(800);
                
                // 比较元素
                if (dataStructure.elements[j] > dataStructure.elements[j + 1]) {
                    // 高亮将要交换的元素
                    dataStructure.highlightElement(j, 0xff6b6b);     // 红色
                    dataStructure.highlightElement(j + 1, 0xff6b6b); // 红色
                    
                    await this.delay(400);
                    
                    // 执行交换
                    if (dataStructure.swapElements) {
                        await dataStructure.swapElements(j, j + 1);
                    }
                    
                    swapped = true;
                } else {
                    // 不需要交换，用绿色表示正确顺序
                    dataStructure.highlightElement(j, 0x4CAF50);     // 绿色
                    dataStructure.highlightElement(j + 1, 0x4CAF50); // 绿色
                    
                    await this.delay(400);
                }
                
                // 重置颜色
                dataStructure.resetElementColor(j);
                dataStructure.resetElementColor(j + 1);
                
                await this.delay(200);
            }
            
            // 标记已排序的元素（最后一个位置）
            const sortedIndex = n - i - 1;
            if (sortedIndex < dataStructure.elements.length) {
                dataStructure.highlightElement(sortedIndex, 0x2196F3); // 蓝色表示已排序
            }
            
            // 如果没有交换发生，数组已经排序完成
            if (!swapped) {
                break;
            }
            
            await this.delay(500);
        }
        
        // 标记第一个元素也已排序
        if (dataStructure.elements.length > 0) {
            dataStructure.highlightElement(0, 0x2196F3);
        }
        
        // 最终效果：所有元素变为绿色表示排序完成
        await this.delay(1000);
        for (let i = 0; i < dataStructure.elements.length; i++) {
            dataStructure.highlightElement(i, 0x4CAF50);
            await this.delay(100);
        }
        
        await this.delay(2000);
        
        // 恢复原始颜色
        dataStructure.resetAllColors();
    }
    
    getComplexity() {
        return {
            time: 'O(n²)',
            space: 'O(1)',
            stable: true,
            inPlace: true
        };
    }
}
