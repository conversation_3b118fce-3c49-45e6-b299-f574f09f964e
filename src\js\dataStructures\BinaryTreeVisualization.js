import * as THREE from 'three';
import { gsap } from 'gsap';
import { BaseDataStructure } from './BaseDataStructure.js';

export class BinaryTreeVisualization extends BaseDataStructure {
    constructor(scene) {
        super(scene);
        this.levelHeight = 2.5;
        this.levelWidth = 4;
        this.connections = [];
        this.nodes = new Map(); // 存储节点值到位置的映射
    }
    
    init() {
        super.init();
        this.createTreeBase();
    }
    
    createTreeBase() {
        // 创建树的基础标识
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;
        
        context.fillStyle = '#64b3f4';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = '#ffffff';
        context.font = 'bold 28px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText('BINARY TREE', canvas.width / 2, canvas.height / 2);
        
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);
        
        sprite.scale.set(3, 1.5, 1);
        sprite.position.set(0, -4, 0);
        
        this.scene.add(sprite);
        this.labelSprite = sprite;
    }
    
    addElement(value) {
        if (this.elements.includes(value)) {
            console.warn('Value already exists in tree');
            return;
        }
        
        this.elements.push(value);
        
        // 简化的BST插入逻辑
        const position = this.calculateNodePosition(value);
        const mesh = this.createNodeMesh(value, position);
        
        this.scene.add(mesh);
        this.meshes.push(mesh);
        this.nodes.set(value, { mesh, position, level: position.level });
        
        // 创建连接线
        this.createConnections();
        
        // 动画效果
        gsap.from(mesh.scale, {
            duration: 0.5,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.out(1.7)'
        });
        
        gsap.from(mesh.position, {
            duration: 0.6,
            y: position.y + 2,
            ease: 'bounce.out'
        });
    }
    
    removeElement(value) {
        if (typeof value !== 'number') {
            // 如果传入的是索引，转换为值
            if (value >= 0 && value < this.elements.length) {
                value = this.elements[value];
            } else {
                console.warn('Invalid index');
                return;
            }
        }
        
        const nodeInfo = this.nodes.get(value);
        if (!nodeInfo) {
            console.warn('Value not found in tree');
            return;
        }
        
        const mesh = nodeInfo.mesh;
        
        // 动画效果
        gsap.to(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.in(1.7)',
            onComplete: () => {
                this.scene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            }
        });
        
        // 移除元素
        const index = this.elements.indexOf(value);
        if (index > -1) {
            this.elements.splice(index, 1);
            this.meshes.splice(index, 1);
        }
        this.nodes.delete(value);
        
        // 重新创建连接线
        setTimeout(() => {
            this.createConnections();
        }, 400);
    }
    
    calculateNodePosition(value) {
        // 简化的位置计算 - 按插入顺序分层排列
        const level = Math.floor(Math.log2(this.elements.length + 1));
        const indexInLevel = this.elements.length - Math.pow(2, level) + 1;
        const maxNodesInLevel = Math.pow(2, level);
        
        const x = (indexInLevel - maxNodesInLevel / 2 - 0.5) * (this.levelWidth / Math.max(1, maxNodesInLevel - 1));
        const y = -level * this.levelHeight;
        
        return { x, y, z: 0, level };
    }
    
    createNodeMesh(value, position) {
        // 创建球形节点
        const geometry = new THREE.SphereGeometry(0.5, 16, 12);
        const material = new THREE.MeshLambertMaterial({ 
            color: 0x64b3f4,
            transparent: true,
            opacity: 0.8
        });
        
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(position.x, position.y, position.z);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        
        // 添加文本标签
        this.addTextLabel(mesh, value.toString());
        
        return mesh;
    }
    
    createConnections() {
        // 清除旧连接
        this.connections.forEach(connection => {
            this.scene.remove(connection);
        });
        this.connections = [];
        
        // 简化的连接逻辑 - 按层级连接
        const nodesByLevel = new Map();
        
        this.nodes.forEach((nodeInfo, value) => {
            const level = nodeInfo.level;
            if (!nodesByLevel.has(level)) {
                nodesByLevel.set(level, []);
            }
            nodesByLevel.get(level).push({ value, nodeInfo });
        });
        
        // 连接父子节点
        for (let level = 0; level < nodesByLevel.size - 1; level++) {
            const currentLevel = nodesByLevel.get(level) || [];
            const nextLevel = nodesByLevel.get(level + 1) || [];
            
            currentLevel.forEach((parent, parentIndex) => {
                // 左子节点
                const leftChildIndex = parentIndex * 2;
                if (leftChildIndex < nextLevel.length) {
                    const leftChild = nextLevel[leftChildIndex];
                    const connection = this.createConnection(
                        parent.nodeInfo.position,
                        leftChild.nodeInfo.position
                    );
                    this.scene.add(connection);
                    this.connections.push(connection);
                }
                
                // 右子节点
                const rightChildIndex = parentIndex * 2 + 1;
                if (rightChildIndex < nextLevel.length) {
                    const rightChild = nextLevel[rightChildIndex];
                    const connection = this.createConnection(
                        parent.nodeInfo.position,
                        rightChild.nodeInfo.position
                    );
                    this.scene.add(connection);
                    this.connections.push(connection);
                }
            });
        }
    }
    
    // 高亮节点
    highlightNode(value, color = 0xffff00) {
        const nodeInfo = this.nodes.get(value);
        if (nodeInfo) {
            nodeInfo.mesh.material.color.setHex(color);
            nodeInfo.mesh.material.emissive.setHex(color * 0.1);
        }
    }
    
    // 重置节点颜色
    resetNodeColor(value) {
        const nodeInfo = this.nodes.get(value);
        if (nodeInfo) {
            nodeInfo.mesh.material.color.setHex(0x64b3f4);
            nodeInfo.mesh.material.emissive.setHex(0x000000);
        }
    }
    
    // 重置所有节点颜色
    resetAllColors() {
        this.nodes.forEach((nodeInfo) => {
            nodeInfo.mesh.material.color.setHex(0x64b3f4);
            nodeInfo.mesh.material.emissive.setHex(0x000000);
        });
    }
    
    // 获取所有节点值（用于算法）
    getAllNodes() {
        return Array.from(this.nodes.keys());
    }
    
    // 获取节点网格（用于算法动画）
    getNodeMesh(value) {
        const nodeInfo = this.nodes.get(value);
        return nodeInfo ? nodeInfo.mesh : null;
    }
    
    clear() {
        super.clear();
        
        // 清除连接
        this.connections.forEach(connection => {
            this.scene.remove(connection);
        });
        this.connections = [];
        
        // 清除节点映射
        this.nodes.clear();
    }
    
    dispose() {
        this.clear();
        
        if (this.labelSprite) {
            this.scene.remove(this.labelSprite);
        }
    }
}
