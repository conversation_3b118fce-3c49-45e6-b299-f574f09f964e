<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据结构洞察镜 - Data Structure Insight Lens</title>
    <link rel="stylesheet" href="./src/styles/main.css">
</head>
<body>
    <div id="app">
        <!-- 顶部标题栏 -->
        <header class="header">
            <h1>🔍 数据结构洞察镜</h1>
            <p>Data Structure Insight Lens - 3D Interactive Visualization</p>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧控制面板 -->
            <aside class="control-panel">
                <div class="panel-section">
                    <h3>数据结构选择</h3>
                    <select id="structureSelect" class="control-select">
                        <option value="array">数组 (Array)</option>
                        <option value="linkedlist">链表 (Linked List)</option>
                        <option value="stack">栈 (Stack)</option>
                        <option value="queue">队列 (Queue)</option>
                        <option value="binaryTree">二叉树 (Binary Tree)</option>
                        <option value="graph">图 (Graph)</option>
                    </select>
                </div>

                <div class="panel-section">
                    <h3>数据操作</h3>
                    <div class="input-group">
                        <input type="number" id="valueInput" placeholder="输入数值" class="control-input">
                        <button id="addBtn" class="control-btn primary">添加</button>
                    </div>
                    <div class="input-group">
                        <input type="number" id="indexInput" placeholder="索引/位置" class="control-input">
                        <button id="removeBtn" class="control-btn danger">删除</button>
                    </div>
                    <button id="clearBtn" class="control-btn secondary">清空</button>
                </div>

                <div class="panel-section">
                    <h3>算法演示</h3>
                    <select id="algorithmSelect" class="control-select">
                        <option value="">选择算法</option>
                        <option value="bubbleSort">冒泡排序</option>
                        <option value="quickSort">快速排序</option>
                        <option value="bfs">广度优先搜索</option>
                        <option value="dfs">深度优先搜索</option>
                        <option value="binarySearch">二分查找</option>
                    </select>
                    <button id="startAlgorithm" class="control-btn primary">开始演示</button>
                    <button id="pauseAlgorithm" class="control-btn secondary">暂停</button>
                    <button id="resetAlgorithm" class="control-btn secondary">重置</button>
                </div>

                <div class="panel-section">
                    <h3>视图控制</h3>
                    <div class="speed-control">
                        <label>动画速度: <span id="speedValue">1x</span></label>
                        <input type="range" id="speedSlider" min="0.1" max="3" step="0.1" value="1" class="slider">
                    </div>
                    <button id="resetCamera" class="control-btn secondary">重置视角</button>
                </div>

                <div class="panel-section">
                    <h3>状态信息</h3>
                    <div id="statusInfo" class="status-info">
                        <p>当前结构: <span id="currentStructure">数组</span></p>
                        <p>元素数量: <span id="elementCount">0</span></p>
                        <p>算法状态: <span id="algorithmStatus">就绪</span></p>
                    </div>
                </div>
            </aside>

            <!-- 3D可视化区域 -->
            <section class="visualization-area">
                <canvas id="threeCanvas"></canvas>
                <div id="loadingOverlay" class="loading-overlay">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                </div>
            </section>
        </main>

        <!-- 底部信息栏 -->
        <footer class="footer">
            <div class="algorithm-info" id="algorithmInfo">
                <h4>算法说明</h4>
                <p id="algorithmDescription">选择一个算法查看详细说明</p>
            </div>
        </footer>
    </div>

    <script type="module" src="./src/js/main.js"></script>
</body>
</html>
