# 数据结构洞察镜 (Data Structure Insight Lens)

一个基于Three.js的3D数据结构可视化工具，支持交互式数据结构操作和经典算法演示。

## 🚀 功能特色

### 📊 支持的数据结构
- **数组 (Array)** - 线性存储结构，支持索引访问
- **链表 (Linked List)** - 动态链式存储结构
- **栈 (Stack)** - LIFO（后进先出）数据结构
- **队列 (Queue)** - FIFO（先进先出）数据结构
- **二叉树 (Binary Tree)** - 层次化树形结构
- **图 (Graph)** - 节点和边的网络结构

### 🎯 算法演示
- **冒泡排序** - 经典的交换排序算法
- **快速排序** - 高效的分治排序算法
- **二分查找** - 有序数组的快速查找
- **广度优先搜索 (BFS)** - 图和树的层次遍历
- **深度优先搜索 (DFS)** - 图和树的深度遍历

### ✨ 交互功能
- 🎨 **3D可视化** - 美观的3D渲染效果
- 🎮 **实时操作** - 动态添加、删除数据元素
- 🎬 **动画演示** - 平滑的3D动画效果
- 🎛️ **速度控制** - 可调节动画播放速度
- 📹 **算法回放** - 支持暂停、重置操作
- 🎪 **视角控制** - 自由旋转、缩放视角

## 🛠️ 技术栈

- **Three.js** - 3D图形渲染引擎
- **GSAP** - 高性能动画库
- **Vite** - 现代化构建工具
- **ES6+** - 现代JavaScript语法

## 📦 安装与运行

### 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0

### 快速开始

1. **克隆项目**
   ```bash
   git clone https://github.com/zym9863/Data-Structure-Insight-Lens.git
   cd Data-Structure-Insight-Lens
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **打开浏览器**
   访问 `http://localhost:3000`

### 构建生产版本
```bash
npm run build
```

## 🎮 使用指南

### 基本操作

1. **选择数据结构**
   - 在左侧面板选择要可视化的数据结构类型

2. **添加数据**
   - 在"数据操作"区域输入数值
   - 点击"添加"按钮将数据加入结构

3. **删除数据**
   - 输入要删除的索引或位置
   - 点击"删除"按钮移除数据

4. **运行算法**
   - 选择适用于当前数据结构的算法
   - 点击"开始演示"查看算法执行过程

### 控制面板功能

#### 数据结构选择
- 🔢 **数组** - 适合排序和查找算法
- 🔗 **链表** - 演示链式存储结构
- 📚 **栈** - LIFO操作演示
- 🚇 **队列** - FIFO操作演示
- 🌳 **二叉树** - 树的遍历算法
- 🕸️ **图** - 图搜索算法

#### 算法控制
- ▶️ **开始演示** - 启动算法可视化
- ⏸️ **暂停** - 暂停当前执行
- 🔄 **重置** - 重置到初始状态

#### 视图控制
- 🎚️ **动画速度** - 调节播放速度 (0.1x - 3x)
- 📷 **重置视角** - 恢复默认相机位置

### 键盘快捷键
- `空格键` - 开始/暂停算法演示
- `Ctrl + R` - 重置算法状态
- `Ctrl + C` - 清空当前数据结构

## 🎨 界面说明

### 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│                    🔍 数据结构洞察镜                        │
├───────────────┬─────────────────────────────────────────┤
│   控制面板      │              3D可视化区域                │
│               │                                       │
│ • 数据结构选择   │         [3D渲染画布]                    │
│ • 数据操作      │                                       │
│ • 算法演示      │                                       │
│ • 视图控制      │                                       │
│ • 状态信息      │                                       │
├───────────────┴─────────────────────────────────────────┤
│                     算法说明区域                          │
└─────────────────────────────────────────────────────────┘
```

### 颜色编码说明
- 🔵 **蓝色** - 默认元素状态
- 🟡 **黄色** - 当前处理的元素
- 🔴 **红色** - 需要交换的元素
- 🟢 **绿色** - 正确位置或完成状态
- 🟠 **橙色** - 基准元素或当前节点
- 🟣 **紫色** - 特殊状态或区间标记

## 🔧 开发说明

### 项目结构
```
Data-Structure-Insight-Lens/
├── index.html                 # 主页面
├── package.json              # 项目配置
├── vite.config.js            # Vite配置
├── src/
│   ├── styles/
│   │   └── main.css          # 主样式文件
│   └── js/
│       ├── main.js           # 应用入口
│       ├── dataStructures/   # 数据结构类
│       │   ├── BaseDataStructure.js
│       │   ├── ArrayVisualization.js
│       │   ├── LinkedListVisualization.js
│       │   ├── StackVisualization.js
│       │   ├── QueueVisualization.js
│       │   ├── BinaryTreeVisualization.js
│       │   └── GraphVisualization.js
│       └── algorithms/       # 算法类
│           ├── BaseAlgorithm.js
│           ├── BubbleSortAlgorithm.js
│           ├── QuickSortAlgorithm.js
│           ├── BFSAlgorithm.js
│           ├── DFSAlgorithm.js
│           └── BinarySearchAlgorithm.js
```

### 扩展指南

#### 添加新的数据结构
1. 继承 `BaseDataStructure` 类
2. 实现必要的方法：`addElement()`, `removeElement()`, `clear()`
3. 在 `main.js` 中注册新的数据结构

#### 添加新的算法
1. 继承 `BaseAlgorithm` 类
2. 实现 `run()` 方法
3. 在 `main.js` 中注册新的算法

## 🎯 未来规划

- [ ] 支持更多数据结构（哈希表、堆等）
- [ ] 增加更多排序算法（归并排序、堆排序等）
- [ ] 支持自定义数据结构
- [ ] 添加算法复杂度分析
- [ ] 支持算法执行步骤记录和回放
- [ ] 添加音效和更丰富的视觉效果
- [ ] 支持多语言界面
- [ ] 添加算法性能对比功能

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来帮助改进这个项目！

## 📞 支持

如果您在使用过程中遇到问题，请：
1. 查看控制台错误信息
2. 确保浏览器支持WebGL
3. 检查网络连接是否正常

---

**享受数据结构和算法的3D可视化之旅！** 🚀✨
