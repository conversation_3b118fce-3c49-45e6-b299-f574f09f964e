import * as THREE from 'three';
import { gsap } from 'gsap';
import { BaseDataStructure } from './BaseDataStructure.js';

export class GraphVisualization extends BaseDataStructure {
    constructor(scene) {
        super(scene);
        this.radius = 4; // 图的半径
        this.connections = [];
        this.edges = new Set(); // 存储边的信息
        this.nodes = new Map(); // 存储节点值到位置的映射
    }
    
    init() {
        super.init();
        this.createGraphBase();
    }
    
    createGraphBase() {
        // 创建图的基础标识
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;
        
        context.fillStyle = '#64b3f4';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = '#ffffff';
        context.font = 'bold 32px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText('GRAPH', canvas.width / 2, canvas.height / 2);
        
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);
        
        sprite.scale.set(2, 1, 1);
        sprite.position.set(0, -6, 0);
        
        this.scene.add(sprite);
        this.labelSprite = sprite;
    }
    
    addElement(value) {
        if (this.elements.includes(value)) {
            console.warn('Node already exists in graph');
            return;
        }
        
        this.elements.push(value);
        
        // 计算节点位置（圆形排列）
        const position = this.calculateNodePosition(this.elements.length - 1);
        const mesh = this.createNodeMesh(value, position);
        
        this.scene.add(mesh);
        this.meshes.push(mesh);
        this.nodes.set(value, { mesh, position, index: this.elements.length - 1 });
        
        // 自动与相邻节点创建连接（简化逻辑）
        if (this.elements.length > 1) {
            this.autoConnectNodes(value);
        }
        
        // 动画效果
        gsap.from(mesh.scale, {
            duration: 0.5,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.out(1.7)'
        });
        
        gsap.from(mesh.position, {
            duration: 0.6,
            x: 0,
            y: 0,
            z: 0,
            ease: 'power2.out'
        });
    }
    
    removeElement(value) {
        if (typeof value !== 'number') {
            // 如果传入的是索引，转换为值
            if (value >= 0 && value < this.elements.length) {
                value = this.elements[value];
            } else {
                console.warn('Invalid index');
                return;
            }
        }
        
        const nodeInfo = this.nodes.get(value);
        if (!nodeInfo) {
            console.warn('Node not found in graph');
            return;
        }
        
        const mesh = nodeInfo.mesh;
        
        // 动画效果
        gsap.to(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.in(1.7)',
            onComplete: () => {
                this.scene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            }
        });
        
        // 移除相关边
        this.removeNodeConnections(value);
        
        // 移除元素
        const index = this.elements.indexOf(value);
        if (index > -1) {
            this.elements.splice(index, 1);
            this.meshes.splice(index, 1);
        }
        this.nodes.delete(value);
        
        // 重新排列剩余节点
        setTimeout(() => {
            this.rearrangeNodes();
        }, 400);
    }
    
    calculateNodePosition(index) {
        const angle = (index * 2 * Math.PI) / Math.max(this.elements.length, 3);
        const x = this.radius * Math.cos(angle);
        const z = this.radius * Math.sin(angle);
        
        return { x, y: 0, z };
    }
    
    createNodeMesh(value, position) {
        // 创建球形节点
        const geometry = new THREE.SphereGeometry(0.6, 16, 12);
        const material = new THREE.MeshLambertMaterial({ 
            color: 0x64b3f4,
            transparent: true,
            opacity: 0.8
        });
        
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(position.x, position.y, position.z);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        
        // 添加文本标签
        this.addTextLabel(mesh, value.toString());
        
        return mesh;
    }
    
    autoConnectNodes(newValue) {
        // 简化的自动连接逻辑：新节点与前一个节点连接
        if (this.elements.length >= 2) {
            const prevValue = this.elements[this.elements.length - 2];
            this.addEdge(prevValue, newValue);
        }
        
        // 如果是第三个或更多节点，随机连接一个额外的节点
        if (this.elements.length >= 3 && Math.random() > 0.5) {
            const randomIndex = Math.floor(Math.random() * (this.elements.length - 1));
            const randomValue = this.elements[randomIndex];
            if (randomValue !== newValue) {
                this.addEdge(randomValue, newValue);
            }
        }
    }
    
    addEdge(value1, value2) {
        const edgeKey = `${Math.min(value1, value2)}-${Math.max(value1, value2)}`;
        if (this.edges.has(edgeKey)) {
            return; // 边已存在
        }
        
        const node1Info = this.nodes.get(value1);
        const node2Info = this.nodes.get(value2);
        
        if (node1Info && node2Info) {
            const connection = this.createConnection(node1Info.position, node2Info.position);
            this.scene.add(connection);
            this.connections.push(connection);
            this.edges.add(edgeKey);
        }
    }
    
    removeNodeConnections(nodeValue) {
        // 移除与指定节点相关的所有边
        const edgesToRemove = [];
        
        this.edges.forEach(edgeKey => {
            const [v1, v2] = edgeKey.split('-').map(Number);
            if (v1 === nodeValue || v2 === nodeValue) {
                edgesToRemove.push(edgeKey);
            }
        });
        
        edgesToRemove.forEach(edgeKey => {
            this.edges.delete(edgeKey);
        });
        
        // 重新创建连接
        this.recreateConnections();
    }
    
    recreateConnections() {
        // 清除所有连接
        this.connections.forEach(connection => {
            this.scene.remove(connection);
        });
        this.connections = [];
        
        // 重新创建所有边
        this.edges.forEach(edgeKey => {
            const [v1, v2] = edgeKey.split('-').map(Number);
            const node1Info = this.nodes.get(v1);
            const node2Info = this.nodes.get(v2);
            
            if (node1Info && node2Info) {
                const connection = this.createConnection(node1Info.position, node2Info.position);
                this.scene.add(connection);
                this.connections.push(connection);
            }
        });
    }
    
    rearrangeNodes() {
        // 重新计算所有节点位置
        this.elements.forEach((value, index) => {
            const nodeInfo = this.nodes.get(value);
            if (nodeInfo) {
                const newPosition = this.calculateNodePosition(index);
                nodeInfo.position = newPosition;
                nodeInfo.index = index;
                
                gsap.to(nodeInfo.mesh.position, {
                    duration: 0.8,
                    x: newPosition.x,
                    y: newPosition.y,
                    z: newPosition.z,
                    ease: 'power2.out'
                });
            }
        });
        
        // 延迟重新创建连接
        setTimeout(() => {
            this.recreateConnections();
        }, 800);
    }
    
    // 高亮节点
    highlightNode(value, color = 0xffff00) {
        const nodeInfo = this.nodes.get(value);
        if (nodeInfo) {
            nodeInfo.mesh.material.color.setHex(color);
            nodeInfo.mesh.material.emissive.setHex(color * 0.1);
        }
    }
    
    // 重置节点颜色
    resetNodeColor(value) {
        const nodeInfo = this.nodes.get(value);
        if (nodeInfo) {
            nodeInfo.mesh.material.color.setHex(0x64b3f4);
            nodeInfo.mesh.material.emissive.setHex(0x000000);
        }
    }
    
    // 重置所有节点颜色
    resetAllColors() {
        this.nodes.forEach((nodeInfo) => {
            nodeInfo.mesh.material.color.setHex(0x64b3f4);
            nodeInfo.mesh.material.emissive.setHex(0x000000);
        });
    }
    
    // 获取所有节点值（用于算法）
    getAllNodes() {
        return Array.from(this.nodes.keys());
    }
    
    // 获取节点的邻居
    getNeighbors(nodeValue) {
        const neighbors = [];
        
        this.edges.forEach(edgeKey => {
            const [v1, v2] = edgeKey.split('-').map(Number);
            if (v1 === nodeValue) {
                neighbors.push(v2);
            } else if (v2 === nodeValue) {
                neighbors.push(v1);
            }
        });
        
        return neighbors;
    }
    
    // 获取节点网格（用于算法动画）
    getNodeMesh(value) {
        const nodeInfo = this.nodes.get(value);
        return nodeInfo ? nodeInfo.mesh : null;
    }
    
    clear() {
        super.clear();
        
        // 清除连接
        this.connections.forEach(connection => {
            this.scene.remove(connection);
        });
        this.connections = [];
        
        // 清除边和节点映射
        this.edges.clear();
        this.nodes.clear();
    }
    
    dispose() {
        this.clear();
        
        if (this.labelSprite) {
            this.scene.remove(this.labelSprite);
        }
    }
}
