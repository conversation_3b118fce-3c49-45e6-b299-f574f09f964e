import * as THREE from 'three';

export class BaseDataStructure {
    constructor(scene) {
        this.scene = scene;
        this.elements = [];
        this.meshes = [];
        this.animations = [];
    }
    
    init() {
        this.clear();
    }
    
    addElement(value) {
        throw new Error('addElement method must be implemented');
    }
    
    removeElement(index) {
        throw new Error('removeElement method must be implemented');
    }
    
    clear() {
        // 清除所有网格
        this.meshes.forEach(mesh => {
            this.scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) mesh.material.dispose();
        });
        this.meshes = [];
        this.elements = [];
    }
    
    getElementCount() {
        return this.elements.length;
    }
    
    update() {
        // 更新动画等
    }
    
    dispose() {
        this.clear();
    }
    
    // 辅助方法：创建基本几何体
    createElementMesh(value, position = { x: 0, y: 0, z: 0 }, color = 0x64b3f4) {
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshLambertMaterial({ 
            color: color,
            transparent: true,
            opacity: 0.8
        });
        
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(position.x, position.y, position.z);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        
        // 添加文本标签
        this.addTextLabel(mesh, value.toString());
        
        return mesh;
    }
    
    addTextLabel(mesh, text) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 128;
        
        context.fillStyle = '#ffffff';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = '#000000';
        context.font = 'bold 48px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(text, canvas.width / 2, canvas.height / 2);
        
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);
        
        sprite.scale.set(1, 0.5, 1);
        sprite.position.set(0, 0.7, 0);
        
        mesh.add(sprite);
    }
    
    // 辅助方法：创建连接线
    createConnection(startPos, endPos, color = 0xffffff) {
        const geometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(startPos.x, startPos.y, startPos.z),
            new THREE.Vector3(endPos.x, endPos.y, endPos.z)
        ]);
        
        const material = new THREE.LineBasicMaterial({ 
            color: color,
            linewidth: 2
        });
        
        const line = new THREE.Line(geometry, material);
        return line;
    }
}
