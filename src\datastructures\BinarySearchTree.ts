import * as THREE from 'three'
import { DataStructureBase, DataNode, Connection } from './DataStructureBase'
import { SceneManager } from '../core/SceneManager'

/**
 * 二叉搜索树节点
 */
class BSTNode {
  public value: number
  public left: BSTNode | null = null
  public right: BSTNode | null = null
  public dataNode: DataNode
  public leftConnection: Connection | null = null
  public rightConnection: Connection | null = null

  constructor(value: number, dataNode: DataNode) {
    this.value = value
    this.dataNode = dataNode
  }
}

/**
 * 二叉搜索树实现
 */
export class BinarySearchTree extends DataStructureBase {
  private root: BSTNode | null = null
  private nodeSpacing = 2.5
  private levelHeight = 2

  constructor(sceneManager: SceneManager) {
    super(sceneManager)
  }

  /**
   * 插入节点
   */
  public async insert(value: number): Promise<void> {
    if (this.nodes.has(value)) {
      console.warn(`值 ${value} 已存在`)
      return
    }

    // 创建新的数据节点
    const position = new THREE.Vector3(0, 0, 0)
    const dataNode = new DataNode(value, position, this.scene)
    this.nodes.set(value, dataNode)

    if (!this.root) {
      // 如果是根节点
      this.root = new BSTNode(value, dataNode)
      await this.createAnimation(dataNode.getMesh(), new THREE.Vector3(0, 0, 0))
      await this.createAnimation(dataNode.getTextSprite(), new THREE.Vector3(0, 0.6, 0))
    } else {
      // 插入到树中
      await this.insertNode(this.root, value, dataNode, 0, 0)
    }

    // 重新布局整个树
    await this.layoutTree()
  }

  /**
   * 递归插入节点
   */
  private async insertNode(
    current: BSTNode, 
    value: number, 
    dataNode: DataNode, 
    level: number, 
    horizontalOffset: number
  ): Promise<BSTNode> {
    // 高亮当前比较的节点
    this.highlightNode(current.dataNode, 0xffeb3b)
    await this.delay(500)

    if (value < current.value) {
      if (!current.left) {
        // 插入到左子树
        current.left = new BSTNode(value, dataNode)
        const targetPosition = new THREE.Vector3(
          horizontalOffset - this.nodeSpacing,
          -level * this.levelHeight,
          0
        )
        await this.createAnimation(dataNode.getMesh(), targetPosition)
        await this.createAnimation(dataNode.getTextSprite(), 
          new THREE.Vector3(targetPosition.x, targetPosition.y + 0.6, targetPosition.z))
        
        // 创建连接线
        current.leftConnection = new Connection(
          current.dataNode.getPosition(),
          current.left.dataNode.getPosition(),
          this.scene
        )
      } else {
        await this.insertNode(current.left, value, dataNode, level + 1, horizontalOffset - this.nodeSpacing)
      }
    } else {
      if (!current.right) {
        // 插入到右子树
        current.right = new BSTNode(value, dataNode)
        const targetPosition = new THREE.Vector3(
          horizontalOffset + this.nodeSpacing,
          -level * this.levelHeight,
          0
        )
        await this.createAnimation(dataNode.getMesh(), targetPosition)
        await this.createAnimation(dataNode.getTextSprite(), 
          new THREE.Vector3(targetPosition.x, targetPosition.y + 0.6, targetPosition.z))
        
        // 创建连接线
        current.rightConnection = new Connection(
          current.dataNode.getPosition(),
          current.right.dataNode.getPosition(),
          this.scene
        )
      } else {
        await this.insertNode(current.right, value, dataNode, level + 1, horizontalOffset + this.nodeSpacing)
      }
    }

    // 取消高亮
    this.unhighlightNode(current.dataNode)
    return current
  }

  /**
   * 删除节点
   */
  public async delete(value: number): Promise<void> {
    if (!this.nodes.has(value)) {
      console.warn(`值 ${value} 不存在`)
      return
    }

    this.root = await this.deleteNode(this.root, value)
    await this.layoutTree()
  }

  /**
   * 递归删除节点
   */
  private async deleteNode(node: BSTNode | null, value: number): Promise<BSTNode | null> {
    if (!node) return null

    // 高亮当前节点
    this.highlightNode(node.dataNode, 0xff6b6b)
    await this.delay(500)

    if (value < node.value) {
      node.left = await this.deleteNode(node.left, value)
    } else if (value > node.value) {
      node.right = await this.deleteNode(node.right, value)
    } else {
      // 找到要删除的节点
      if (!node.left && !node.right) {
        // 叶子节点
        this.removeNodeFromScene(node)
        return null
      } else if (!node.left) {
        // 只有右子树
        this.removeNodeFromScene(node)
        return node.right
      } else if (!node.right) {
        // 只有左子树
        this.removeNodeFromScene(node)
        return node.left
      } else {
        // 有两个子树，找到右子树的最小值
        const minNode = this.findMin(node.right)
        node.value = minNode.value
        
        // 更新节点显示的值
        this.updateNodeValue(node, minNode.value)
        
        // 删除右子树中的最小节点
        node.right = await this.deleteNode(node.right, minNode.value)
      }
    }

    this.unhighlightNode(node.dataNode)
    return node
  }

  /**
   * 查找最小值节点
   */
  private findMin(node: BSTNode): BSTNode {
    while (node.left) {
      node = node.left
    }
    return node
  }

  /**
   * 从场景中移除节点
   */
  private removeNodeFromScene(node: BSTNode): void {
    this.nodes.delete(node.value)
    node.dataNode.destroy()
    
    if (node.leftConnection) {
      node.leftConnection.destroy()
    }
    if (node.rightConnection) {
      node.rightConnection.destroy()
    }
  }

  /**
   * 更新节点显示的值
   */
  private updateNodeValue(node: BSTNode, newValue: number): void {
    // 这里需要重新创建文本精灵来显示新值
    // 简化实现，实际应用中可以优化
    const oldDataNode = node.dataNode
    const position = oldDataNode.getPosition()
    
    this.nodes.delete(node.value)
    oldDataNode.destroy()
    
    const newDataNode = new DataNode(newValue, position, this.scene)
    this.nodes.set(newValue, newDataNode)
    node.dataNode = newDataNode
    node.value = newValue
  }

  /**
   * 搜索节点
   */
  public async search(value: number): Promise<boolean> {
    return await this.searchNode(this.root, value)
  }

  /**
   * 递归搜索节点
   */
  private async searchNode(node: BSTNode | null, value: number): Promise<boolean> {
    if (!node) {
      console.log(`值 ${value} 未找到`)
      return false
    }

    // 高亮当前搜索的节点
    this.highlightNode(node.dataNode, 0x4caf50)
    await this.delay(800)

    if (value === node.value) {
      console.log(`找到值 ${value}`)
      // 保持高亮一段时间
      await this.delay(1000)
      this.unhighlightNode(node.dataNode)
      return true
    } else if (value < node.value) {
      this.unhighlightNode(node.dataNode)
      return await this.searchNode(node.left, value)
    } else {
      this.unhighlightNode(node.dataNode)
      return await this.searchNode(node.right, value)
    }
  }

  /**
   * 清空树
   */
  public async clear(): Promise<void> {
    if (this.root) {
      await this.clearNode(this.root)
      this.root = null
    }
  }

  /**
   * 递归清空节点
   */
  private async clearNode(node: BSTNode): Promise<void> {
    if (node.left) {
      await this.clearNode(node.left)
    }
    if (node.right) {
      await this.clearNode(node.right)
    }
    
    this.removeNodeFromScene(node)
    await this.delay(100)
  }

  /**
   * 重新布局整个树
   */
  private async layoutTree(): Promise<void> {
    if (!this.root) return

    // 计算每个节点的位置
    const positions = new Map<BSTNode, THREE.Vector3>()
    this.calculatePositions(this.root, 0, 0, positions)

    // 动画移动所有节点到新位置
    const animations: Promise<void>[] = []
    
    positions.forEach((position, node) => {
      animations.push(this.createAnimation(node.dataNode.getMesh(), position, 800))
      animations.push(this.createAnimation(node.dataNode.getTextSprite(), 
        new THREE.Vector3(position.x, position.y + 0.6, position.z), 800))
    })

    await Promise.all(animations)

    // 更新连接线
    this.updateConnections(this.root)
  }

  /**
   * 计算节点位置
   */
  private calculatePositions(
    node: BSTNode, 
    level: number, 
    horizontalOffset: number, 
    positions: Map<BSTNode, THREE.Vector3>
  ): number {
    if (!node) return 0

    let leftWidth = 0
    let rightWidth = 0

    if (node.left) {
      leftWidth = this.calculatePositions(node.left, level + 1, horizontalOffset, positions)
    }

    const nodeX = horizontalOffset + leftWidth
    const nodeY = -level * this.levelHeight
    positions.set(node, new THREE.Vector3(nodeX, nodeY, 0))

    if (node.right) {
      rightWidth = this.calculatePositions(node.right, level + 1, nodeX + this.nodeSpacing, positions)
    }

    return leftWidth + this.nodeSpacing + rightWidth
  }

  /**
   * 更新连接线
   */
  private updateConnections(node: BSTNode): void {
    if (node.left) {
      if (!node.leftConnection) {
        node.leftConnection = new Connection(
          node.dataNode.getPosition(),
          node.left.dataNode.getPosition(),
          this.scene
        )
      } else {
        node.leftConnection.updatePositions(
          node.dataNode.getPosition(),
          node.left.dataNode.getPosition()
        )
      }
      this.updateConnections(node.left)
    }

    if (node.right) {
      if (!node.rightConnection) {
        node.rightConnection = new Connection(
          node.dataNode.getPosition(),
          node.right.dataNode.getPosition(),
          this.scene
        )
      } else {
        node.rightConnection.updatePositions(
          node.dataNode.getPosition(),
          node.right.dataNode.getPosition()
        )
      }
      this.updateConnections(node.right)
    }
  }

  /**
   * 中序遍历
   */
  public async executeInorderTraversal(speed: number): Promise<void> {
    const result: number[] = []
    await this.inorderTraversal(this.root, result, speed)
    console.log('中序遍历结果:', result)
  }

  private async inorderTraversal(node: BSTNode | null, result: number[], speed: number): Promise<void> {
    if (!node) return

    await this.inorderTraversal(node.left, result, speed)
    
    this.highlightNode(node.dataNode, 0x9c27b0)
    result.push(node.value)
    await this.delay(1000 / speed)
    this.unhighlightNode(node.dataNode)
    
    await this.inorderTraversal(node.right, result, speed)
  }

  /**
   * 前序遍历
   */
  public async executePreorderTraversal(speed: number): Promise<void> {
    const result: number[] = []
    await this.preorderTraversal(this.root, result, speed)
    console.log('前序遍历结果:', result)
  }

  private async preorderTraversal(node: BSTNode | null, result: number[], speed: number): Promise<void> {
    if (!node) return

    this.highlightNode(node.dataNode, 0xff9800)
    result.push(node.value)
    await this.delay(1000 / speed)
    this.unhighlightNode(node.dataNode)
    
    await this.preorderTraversal(node.left, result, speed)
    await this.preorderTraversal(node.right, result, speed)
  }

  /**
   * 后序遍历
   */
  public async executePostorderTraversal(speed: number): Promise<void> {
    const result: number[] = []
    await this.postorderTraversal(this.root, result, speed)
    console.log('后序遍历结果:', result)
  }

  private async postorderTraversal(node: BSTNode | null, result: number[], speed: number): Promise<void> {
    if (!node) return

    await this.postorderTraversal(node.left, result, speed)
    await this.postorderTraversal(node.right, result, speed)
    
    this.highlightNode(node.dataNode, 0x795548)
    result.push(node.value)
    await this.delay(1000 / speed)
    this.unhighlightNode(node.dataNode)
  }

  /**
   * 创建弹性动画
   */
  private createSpringAnimation(
    object: THREE.Object3D,
    targetPosition: THREE.Vector3,
    duration: number = 1000
  ): Promise<void> {
    return new Promise((resolve) => {
      const startPosition = object.position.clone()
      const startTime = Date.now()

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        // 弹性缓动函数
        const easeProgress = this.easeOutElastic(progress)

        object.position.lerpVectors(startPosition, targetPosition, easeProgress)

        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          resolve()
        }
      }

      animate()
    })
  }

  /**
   * 弹性缓动函数
   */
  private easeOutElastic(t: number): number {
    const c4 = (2 * Math.PI) / 3
    return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1
  }
}
