import { BaseAlgorithm } from './BaseAlgorithm.js';

export class BinarySearchAlgorithm extends BaseAlgorithm {
    constructor() {
        super();
        this.name = 'Binary Search';
        this.description = '二分查找：在有序数组中快速查找目标值';
        this.targetValue = null;
    }
    
    async run(dataStructure) {
        if (!dataStructure.elements || dataStructure.elements.length === 0) {
            return;
        }
        
        // 重置所有元素颜色
        dataStructure.resetAllColors();
        
        // 选择目标值（中间的元素作为示例）
        const middleIndex = Math.floor(dataStructure.elements.length / 2);
        this.targetValue = dataStructure.elements[middleIndex];
        
        // 首先检查数组是否已排序，如果没有则先排序
        const isSorted = this.isSorted(dataStructure.elements);
        if (!isSorted) {
            // 显示排序提示
            console.log('Array is not sorted, sorting first...');
            
            // 简单的排序（选择排序用于演示）
            await this.sortArray(dataStructure);
            
            await this.delay(1000);
        }
        
        // 高亮目标值
        const targetIndex = dataStructure.elements.indexOf(this.targetValue);
        if (targetIndex !== -1) {
            dataStructure.highlightElement(targetIndex, 0xFF5722); // 深橙色表示目标
        }
        
        await this.delay(1500);
        
        // 开始二分查找
        const result = await this.binarySearch(dataStructure, this.targetValue);
        
        if (result !== -1) {
            // 找到目标，闪烁显示
            for (let i = 0; i < 3; i++) {
                dataStructure.highlightElement(result, 0x4CAF50); // 绿色
                await this.delay(300);
                dataStructure.highlightElement(result, 0xFFFFFF); // 白色
                await this.delay(300);
            }
            dataStructure.highlightElement(result, 0x4CAF50); // 最终绿色
        }
        
        await this.delay(2000);
        dataStructure.resetAllColors();
    }
    
    async binarySearch(dataStructure, target) {
        let left = 0;
        let right = dataStructure.elements.length - 1;
        let steps = 0;
        
        while (left <= right) {
            steps++;
            
            // 检查是否暂停
            await this.waitForResume();
            if (!this.isRunning) return -1;
            
            // 计算中间位置
            const mid = Math.floor((left + right) / 2);
            
            // 清除之前的高亮（除了目标值）
            for (let i = 0; i < dataStructure.elements.length; i++) {
                if (dataStructure.elements[i] !== target) {
                    dataStructure.resetElementColor(i);
                }
            }
            
            // 高亮搜索范围
            for (let i = left; i <= right; i++) {
                if (dataStructure.elements[i] !== target) {
                    dataStructure.highlightElement(i, 0xE3F2FD); // 淡蓝色表示搜索范围
                }
            }
            
            await this.delay(800);
            
            // 高亮中间元素
            dataStructure.highlightElement(mid, 0xFFEB3B); // 黄色表示当前检查的中间元素
            
            await this.delay(1200);
            
            // 比较中间元素与目标值
            if (dataStructure.elements[mid] === target) {
                // 找到目标
                dataStructure.highlightElement(mid, 0x4CAF50); // 绿色表示找到
                return mid;
            } else if (dataStructure.elements[mid] < target) {
                // 中间值小于目标，搜索右半部分
                dataStructure.highlightElement(mid, 0xFF9800); // 橙色表示太小
                
                // 淡化左半部分
                for (let i = left; i <= mid; i++) {
                    if (dataStructure.elements[i] !== target) {
                        dataStructure.highlightElement(i, 0xBDBDBD); // 灰色表示已排除
                    }
                }
                
                left = mid + 1;
            } else {
                // 中间值大于目标，搜索左半部分
                dataStructure.highlightElement(mid, 0x9C27B0); // 紫色表示太大
                
                // 淡化右半部分
                for (let i = mid; i <= right; i++) {
                    if (dataStructure.elements[i] !== target) {
                        dataStructure.highlightElement(i, 0xBDBDBD); // 灰色表示已排除
                    }
                }
                
                right = mid - 1;
            }
            
            await this.delay(1000);
        }
        
        // 未找到目标
        return -1;
    }
    
    async sortArray(dataStructure) {
        // 简单的选择排序用于演示
        const n = dataStructure.elements.length;
        
        for (let i = 0; i < n - 1; i++) {
            let minIndex = i;
            
            // 高亮当前位置
            dataStructure.highlightElement(i, 0xFF9800); // 橙色
            
            for (let j = i + 1; j < n; j++) {
                // 检查是否暂停
                await this.waitForResume();
                if (!this.isRunning) return;
                
                dataStructure.highlightElement(j, 0xFFEB3B); // 黄色
                await this.delay(200);
                
                if (dataStructure.elements[j] < dataStructure.elements[minIndex]) {
                    if (minIndex !== i) {
                        dataStructure.resetElementColor(minIndex);
                    }
                    minIndex = j;
                    dataStructure.highlightElement(minIndex, 0x4CAF50); // 绿色表示当前最小值
                } else {
                    dataStructure.resetElementColor(j);
                }
            }
            
            // 交换元素
            if (minIndex !== i && dataStructure.swapElements) {
                await dataStructure.swapElements(i, minIndex);
            }
            
            // 标记已排序的位置
            dataStructure.highlightElement(i, 0x2196F3); // 蓝色表示已排序
            
            await this.delay(300);
        }
        
        // 标记最后一个元素也已排序
        dataStructure.highlightElement(n - 1, 0x2196F3);
        
        await this.delay(500);
        dataStructure.resetAllColors();
    }
    
    isSorted(array) {
        for (let i = 1; i < array.length; i++) {
            if (array[i] < array[i - 1]) {
                return false;
            }
        }
        return true;
    }
    
    // 设置自定义目标值
    setTarget(target) {
        this.targetValue = target;
    }
    
    getComplexity() {
        return {
            time: 'O(log n)',
            space: 'O(1)',
            prerequisite: '数组必须已排序'
        };
    }
}
