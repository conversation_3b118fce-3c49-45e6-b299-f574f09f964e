import { BaseAlgorithm } from './BaseAlgorithm.js';

export class QuickSortAlgorithm extends BaseAlgorithm {
    constructor() {
        super();
        this.name = 'Quick Sort';
        this.description = '快速排序：选择基准元素，分区后递归排序';
        this.recursionStack = [];
    }
    
    async run(dataStructure) {
        if (!dataStructure.elements || dataStructure.elements.length <= 1) {
            return;
        }
        
        // 重置所有元素颜色
        dataStructure.resetAllColors();
        
        await this.quickSort(dataStructure, 0, dataStructure.elements.length - 1);
        
        // 最终效果：所有元素变为绿色表示排序完成
        await this.delay(1000);
        for (let i = 0; i < dataStructure.elements.length; i++) {
            dataStructure.highlightElement(i, 0x4CAF50);
            await this.delay(100);
        }
        
        await this.delay(2000);
        dataStructure.resetAllColors();
    }
    
    async quickSort(dataStructure, low, high) {
        if (low < high) {
            // 检查是否暂停
            await this.waitForResume();
            if (!this.isRunning) return;
            
            // 高亮当前处理的区间
            for (let i = low; i <= high; i++) {
                dataStructure.highlightElement(i, 0xE1BEE7); // 淡紫色表示当前区间
            }
            
            await this.delay(800);
            
            // 分区操作
            const pivotIndex = await this.partition(dataStructure, low, high);
            
            if (pivotIndex === -1) return; // 算法被中断
            
            // 高亮基准元素
            dataStructure.highlightElement(pivotIndex, 0xFF9800); // 橙色表示基准
            
            await this.delay(1000);
            
            // 递归排序左半部分
            await this.quickSort(dataStructure, low, pivotIndex - 1);
            
            // 递归排序右半部分
            await this.quickSort(dataStructure, pivotIndex + 1, high);
            
            // 标记这个区间已完成排序
            for (let i = low; i <= high; i++) {
                dataStructure.highlightElement(i, 0x2196F3); // 蓝色表示已排序
            }
            
            await this.delay(500);
        }
    }
    
    async partition(dataStructure, low, high) {
        // 选择最后一个元素作为基准
        const pivot = dataStructure.elements[high];
        
        // 高亮基准元素
        dataStructure.highlightElement(high, 0xFF5722); // 深橙色表示基准
        
        await this.delay(600);
        
        let i = low - 1; // 较小元素的索引
        
        for (let j = low; j < high; j++) {
            // 检查是否暂停
            await this.waitForResume();
            if (!this.isRunning) return -1;
            
            // 高亮当前比较的元素
            dataStructure.highlightElement(j, 0xFFEB3B); // 黄色
            
            await this.delay(600);
            
            // 如果当前元素小于或等于基准
            if (dataStructure.elements[j] <= pivot) {
                i++;
                
                // 高亮即将交换的位置
                if (i !== j) {
                    dataStructure.highlightElement(i, 0x4CAF50); // 绿色
                    dataStructure.highlightElement(j, 0x4CAF50); // 绿色
                    
                    await this.delay(400);
                    
                    // 交换元素
                    if (dataStructure.swapElements) {
                        await dataStructure.swapElements(i, j);
                    }
                }
            } else {
                // 元素大于基准，用红色表示
                dataStructure.highlightElement(j, 0xF44336); // 红色
                await this.delay(300);
            }
            
            // 重置当前元素颜色
            if (j !== high) { // 不重置基准元素
                dataStructure.resetElementColor(j);
            }
            
            await this.delay(200);
        }
        
        // 将基准元素放到正确位置
        const finalPivotPos = i + 1;
        if (finalPivotPos !== high) {
            dataStructure.highlightElement(finalPivotPos, 0x4CAF50);
            dataStructure.highlightElement(high, 0x4CAF50);
            
            await this.delay(400);
            
            if (dataStructure.swapElements) {
                await dataStructure.swapElements(finalPivotPos, high);
            }
        }
        
        // 高亮基准的最终位置
        dataStructure.highlightElement(finalPivotPos, 0xFF9800); // 橙色
        
        await this.delay(800);
        
        return finalPivotPos;
    }
    
    getComplexity() {
        return {
            time: 'O(n log n) 平均, O(n²) 最坏',
            space: 'O(log n)',
            stable: false,
            inPlace: true
        };
    }
}
