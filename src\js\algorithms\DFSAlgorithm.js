import { BaseAlgorithm } from './BaseAlgorithm.js';

export class DFSAlgorithm extends BaseAlgorithm {
    constructor() {
        super();
        this.name = 'Depth-First Search';
        this.description = '深度优先搜索：沿着路径深入探索，使用栈或递归实现';
    }
    
    async run(dataStructure) {
        const nodes = dataStructure.getAllNodes();
        if (!nodes || nodes.length === 0) {
            return;
        }
        
        // 重置所有节点颜色
        dataStructure.resetAllColors();
        
        // 选择起始节点（第一个节点）
        const startNode = nodes[0];
        const visited = new Set();
        
        // 高亮起始节点
        dataStructure.highlightNode(startNode, 0x4CAF50); // 绿色表示起始节点
        
        await this.delay(1000);
        
        // 开始DFS
        await this.dfsRecursive(dataStructure, startNode, visited);
        
        // 最终效果：所有已访问的节点变为绿色
        await this.delay(1000);
        visited.forEach(node => {
            dataStructure.highlightNode(node, 0x4CAF50);
        });
        
        await this.delay(2000);
        dataStructure.resetAllColors();
    }
    
    async dfsRecursive(dataStructure, currentNode, visited) {
        // 检查是否暂停
        await this.waitForResume();
        if (!this.isRunning) return;
        
        // 如果已访问，返回
        if (visited.has(currentNode)) {
            return;
        }
        
        // 标记为已访问
        visited.add(currentNode);
        
        // 高亮当前访问的节点
        dataStructure.highlightNode(currentNode, 0xFF9800); // 橙色表示当前节点
        
        await this.delay(1000);
        
        // 获取邻居节点
        let neighbors = [];
        
        if (dataStructure.getNeighbors) {
            // 图结构
            neighbors = dataStructure.getNeighbors(currentNode);
        } else if (dataStructure.getAllNodes) {
            // 树结构 - 简化逻辑：相邻的值作为邻居
            const allNodes = dataStructure.getAllNodes();
            const currentIndex = allNodes.indexOf(currentNode);
            
            // 左子节点
            if (currentIndex * 2 + 1 < allNodes.length) {
                neighbors.push(allNodes[currentIndex * 2 + 1]);
            }
            
            // 右子节点
            if (currentIndex * 2 + 2 < allNodes.length) {
                neighbors.push(allNodes[currentIndex * 2 + 2]);
            }
        }
        
        // 递归访问邻居节点
        for (const neighbor of neighbors) {
            if (!visited.has(neighbor)) {
                // 高亮即将访问的邻居
                dataStructure.highlightNode(neighbor, 0xFFEB3B); // 黄色表示即将访问
                
                await this.delay(500);
                
                // 递归访问
                await this.dfsRecursive(dataStructure, neighbor, visited);
                
                // 检查是否算法被中断
                if (!this.isRunning) return;
            }
        }
        
        // 回溯：将当前节点标记为已完成访问
        dataStructure.highlightNode(currentNode, 0x2196F3); // 蓝色表示已访问完成
        
        await this.delay(600);
    }
    
    // 非递归版本的DFS（使用栈）
    async dfsIterative(dataStructure, startNode) {
        const visited = new Set();
        const stack = [startNode];
        
        while (stack.length > 0) {
            // 检查是否暂停
            await this.waitForResume();
            if (!this.isRunning) return;
            
            const currentNode = stack.pop();
            
            if (visited.has(currentNode)) {
                continue;
            }
            
            // 标记为已访问
            visited.add(currentNode);
            
            // 高亮当前访问的节点
            dataStructure.highlightNode(currentNode, 0xFF9800); // 橙色表示当前节点
            
            await this.delay(1000);
            
            // 获取邻居节点
            let neighbors = [];
            
            if (dataStructure.getNeighbors) {
                neighbors = dataStructure.getNeighbors(currentNode);
            } else if (dataStructure.getAllNodes) {
                const allNodes = dataStructure.getAllNodes();
                const currentIndex = allNodes.indexOf(currentNode);
                
                if (currentIndex * 2 + 1 < allNodes.length) {
                    neighbors.push(allNodes[currentIndex * 2 + 1]);
                }
                
                if (currentIndex * 2 + 2 < allNodes.length) {
                    neighbors.push(allNodes[currentIndex * 2 + 2]);
                }
            }
            
            // 将未访问的邻居加入栈中（逆序添加以保持顺序）
            for (let i = neighbors.length - 1; i >= 0; i--) {
                const neighbor = neighbors[i];
                if (!visited.has(neighbor) && !stack.includes(neighbor)) {
                    stack.push(neighbor);
                    
                    // 高亮在栈中的节点
                    dataStructure.highlightNode(neighbor, 0xFFEB3B); // 黄色表示在栈中
                    
                    await this.delay(300);
                }
            }
            
            // 将当前节点标记为已访问
            dataStructure.highlightNode(currentNode, 0x2196F3); // 蓝色表示已访问
            
            await this.delay(600);
        }
        
        return visited;
    }
    
    getComplexity() {
        return {
            time: 'O(V + E)',
            space: 'O(V)',
            description: 'V是顶点数，E是边数'
        };
    }
}
