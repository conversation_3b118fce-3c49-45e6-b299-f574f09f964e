import * as THREE from 'three';
import { gsap } from 'gsap';
import { BaseDataStructure } from './BaseDataStructure.js';

export class StackVisualization extends BaseDataStructure {
    constructor(scene) {
        super(scene);
        this.maxHeight = 15;
        this.elementHeight = 0.8;
        this.elementWidth = 1.5;
    }
    
    init() {
        super.init();
        this.createStackBase();
    }
    
    createStackBase() {
        // 创建栈底座
        const baseGeometry = new THREE.CylinderGeometry(1, 1, 0.2, 16);
        const baseMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            transparent: true,
            opacity: 0.6
        });
        
        this.baseMesh = new THREE.Mesh(baseGeometry, baseMaterial);
        this.baseMesh.position.y = -0.1;
        this.baseMesh.receiveShadow = true;
        
        this.scene.add(this.baseMesh);
        
        // 创建栈标识
        this.createStackLabel();
    }
    
    createStackLabel() {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;
        
        context.fillStyle = '#64b3f4';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = '#ffffff';
        context.font = 'bold 32px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText('STACK', canvas.width / 2, canvas.height / 2);
        
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);
        
        sprite.scale.set(2, 1, 1);
        sprite.position.set(0, -2, 0);
        
        this.scene.add(sprite);
        this.labelSprite = sprite;
    }
    
    addElement(value) {
        if (this.elements.length >= this.maxHeight) {
            console.warn('Stack overflow');
            return;
        }
        
        this.elements.push(value);
        const index = this.elements.length - 1;
        
        // 计算位置
        const position = {
            x: 0,
            y: index * this.elementHeight + this.elementHeight / 2,
            z: 0
        };
        
        // 创建网格
        const mesh = this.createStackElement(value, position);
        
        this.scene.add(mesh);
        this.meshes.push(mesh);
        
        // 入栈动画：从上方落下
        gsap.from(mesh.position, {
            duration: 0.6,
            y: position.y + 3,
            ease: 'bounce.out'
        });
        
        gsap.from(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.out(1.7)'
        });
        
        // 更新栈顶指针
        this.updateTopPointer();
    }
    
    removeElement() {
        if (this.elements.length === 0) {
            console.warn('Stack underflow');
            return;
        }
        
        const index = this.elements.length - 1;
        const mesh = this.meshes[index];
        
        // 出栈动画：向上飞出
        gsap.to(mesh.position, {
            duration: 0.5,
            y: mesh.position.y + 3,
            ease: 'power2.in'
        });
        
        gsap.to(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.in(1.7)',
            onComplete: () => {
                this.scene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            }
        });
        
        // 移除元素
        this.elements.pop();
        this.meshes.pop();
        
        // 更新栈顶指针
        this.updateTopPointer();
    }
    
    createStackElement(value, position) {
        const geometry = new THREE.BoxGeometry(this.elementWidth, this.elementHeight, this.elementWidth);
        const material = new THREE.MeshLambertMaterial({ 
            color: this.getElementColor(this.elements.length - 1),
            transparent: true,
            opacity: 0.8
        });
        
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(position.x, position.y, position.z);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        
        // 添加文本标签
        this.addTextLabel(mesh, value.toString());
        
        return mesh;
    }
    
    getElementColor(index) {
        // 根据高度生成渐变色
        const ratio = index / this.maxHeight;
        const hue = 0.6 - ratio * 0.3; // 从蓝色到红色
        return new THREE.Color().setHSL(hue, 0.7, 0.6).getHex();
    }
    
    updateTopPointer() {
        // 移除旧的栈顶指针
        if (this.topPointer) {
            this.scene.remove(this.topPointer);
        }
        
        if (this.elements.length > 0) {
            // 创建栈顶指针
            const topIndex = this.elements.length - 1;
            const topY = topIndex * this.elementHeight + this.elementHeight / 2;
            
            const arrowGeometry = new THREE.ConeGeometry(0.2, 0.6, 8);
            const arrowMaterial = new THREE.MeshLambertMaterial({ color: 0xff6b6b });
            this.topPointer = new THREE.Mesh(arrowGeometry, arrowMaterial);
            
            this.topPointer.position.set(2, topY, 0);
            this.topPointer.rotation.z = -Math.PI / 2;
            
            this.scene.add(this.topPointer);
            
            // 添加 TOP 标签
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 128;
            canvas.height = 64;
            
            context.fillStyle = '#ff6b6b';
            context.fillRect(0, 0, canvas.width, canvas.height);
            
            context.fillStyle = '#ffffff';
            context.font = 'bold 24px Arial';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillText('TOP', canvas.width / 2, canvas.height / 2);
            
            const texture = new THREE.CanvasTexture(canvas);
            const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
            const sprite = new THREE.Sprite(spriteMaterial);
            
            sprite.scale.set(1, 0.5, 1);
            sprite.position.set(3.5, topY, 0);
            
            this.scene.add(sprite);
            this.topLabel = sprite;
        } else {
            // 移除 TOP 标签
            if (this.topLabel) {
                this.scene.remove(this.topLabel);
                this.topLabel = null;
            }
        }
    }
    
    // 压栈操作（别名）
    push(value) {
        this.addElement(value);
    }
    
    // 弹栈操作（别名）
    pop() {
        this.removeElement();
        return this.elements.length > 0 ? this.elements[this.elements.length - 1] : null;
    }
    
    // 查看栈顶元素
    peek() {
        return this.elements.length > 0 ? this.elements[this.elements.length - 1] : null;
    }
    
    // 检查栈是否为空
    isEmpty() {
        return this.elements.length === 0;
    }
    
    // 高亮栈顶元素
    highlightTop(color = 0xffff00) {
        if (this.meshes.length > 0) {
            const topMesh = this.meshes[this.meshes.length - 1];
            topMesh.material.color.setHex(color);
            topMesh.material.emissive.setHex(color * 0.1);
        }
    }
    
    // 重置栈顶元素颜色
    resetTopColor() {
        if (this.meshes.length > 0) {
            const topIndex = this.meshes.length - 1;
            const topMesh = this.meshes[topIndex];
            topMesh.material.color.setHex(this.getElementColor(topIndex));
            topMesh.material.emissive.setHex(0x000000);
        }
    }
    
    clear() {
        super.clear();
        
        // 清除栈顶指针
        if (this.topPointer) {
            this.scene.remove(this.topPointer);
            this.topPointer = null;
        }
        
        if (this.topLabel) {
            this.scene.remove(this.topLabel);
            this.topLabel = null;
        }
    }
    
    dispose() {
        this.clear();
        
        if (this.baseMesh) {
            this.scene.remove(this.baseMesh);
            this.baseMesh.geometry.dispose();
            this.baseMesh.material.dispose();
        }
        
        if (this.labelSprite) {
            this.scene.remove(this.labelSprite);
        }
    }
}
