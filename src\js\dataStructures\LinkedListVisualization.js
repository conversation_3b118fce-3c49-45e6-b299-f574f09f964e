import * as THREE from 'three';
import { gsap } from 'gsap';
import { BaseDataStructure } from './BaseDataStructure.js';

export class LinkedListVisualization extends BaseDataStructure {
    constructor(scene) {
        super(scene);
        this.spacing = 2.5;
        this.connections = []; // 存储连接线
    }
    
    init() {
        super.init();
    }
    
    addElement(value) {
        this.elements.push(value);
        const index = this.elements.length - 1;
        
        // 计算位置
        const position = this.getElementPosition(index);
        
        // 创建节点网格
        const mesh = this.createNodeMesh(value, position);
        
        this.scene.add(mesh);
        this.meshes.push(mesh);
        
        // 添加连接线（如果不是第一个元素）
        if (index > 0) {
            this.createConnection(index - 1, index);
        }
        
        // 动画效果
        gsap.from(mesh.scale, {
            duration: 0.5,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.out(1.7)'
        });
        
        gsap.from(mesh.position, {
            duration: 0.6,
            x: position.x + 2,
            ease: 'power2.out'
        });
    }
    
    removeElement(index) {
        if (index < 0 || index >= this.elements.length) {
            console.warn('Invalid index');
            return;
        }
        
        const mesh = this.meshes[index];
        
        // 动画效果
        gsap.to(mesh.scale, {
            duration: 0.4,
            x: 0,
            y: 0,
            z: 0,
            ease: 'back.in(1.7)',
            onComplete: () => {
                this.scene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            }
        });
        
        // 移除元素和网格
        this.elements.splice(index, 1);
        this.meshes.splice(index, 1);
        
        // 重新创建连接
        this.updateConnections();
        
        // 重新排列元素
        this.rearrangeElements();
    }
    
    createNodeMesh(value, position) {
        // 创建节点组
        const group = new THREE.Group();
        
        // 数据部分
        const dataGeometry = new THREE.BoxGeometry(1.2, 0.8, 0.8);
        const dataMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x64b3f4,
            transparent: true,
            opacity: 0.8
        });
        const dataMesh = new THREE.Mesh(dataGeometry, dataMaterial);
        dataMesh.position.set(-0.3, 0, 0);
        dataMesh.castShadow = true;
        dataMesh.receiveShadow = true;
        
        // 指针部分
        const pointerGeometry = new THREE.BoxGeometry(0.6, 0.8, 0.8);
        const pointerMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x4a90e2,
            transparent: true,
            opacity: 0.8
        });
        const pointerMesh = new THREE.Mesh(pointerGeometry, pointerMaterial);
        pointerMesh.position.set(0.6, 0, 0);
        pointerMesh.castShadow = true;
        pointerMesh.receiveShadow = true;
        
        group.add(dataMesh);
        group.add(pointerMesh);
        
        // 添加文本标签
        this.addTextLabel(dataMesh, value.toString());
        
        group.position.set(position.x, position.y, position.z);
        
        return group;
    }
    
    createConnection(fromIndex, toIndex) {
        if (fromIndex >= this.meshes.length || toIndex >= this.meshes.length) return;
        
        const fromPos = this.getElementPosition(fromIndex);
        const toPos = this.getElementPosition(toIndex);
        
        // 箭头起点和终点
        const startPos = new THREE.Vector3(fromPos.x + 0.9, fromPos.y, fromPos.z);
        const endPos = new THREE.Vector3(toPos.x - 0.9, toPos.y, toPos.z);
        
        // 创建箭头
        const arrow = this.createArrow(startPos, endPos);
        this.scene.add(arrow);
        this.connections.push(arrow);
    }
    
    createArrow(startPos, endPos) {
        const group = new THREE.Group();
        
        // 箭头线条
        const geometry = new THREE.BufferGeometry().setFromPoints([startPos, endPos]);
        const material = new THREE.LineBasicMaterial({ 
            color: 0xffffff,
            linewidth: 3
        });
        const line = new THREE.Line(geometry, material);
        group.add(line);
        
        // 箭头头部
        const direction = new THREE.Vector3().subVectors(endPos, startPos).normalize();
        const arrowGeometry = new THREE.ConeGeometry(0.1, 0.3, 8);
        const arrowMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });
        const arrowHead = new THREE.Mesh(arrowGeometry, arrowMaterial);
        
        arrowHead.position.copy(endPos);
        arrowHead.lookAt(endPos.clone().add(direction));
        arrowHead.rotateX(Math.PI / 2);
        
        group.add(arrowHead);
        
        return group;
    }
    
    updateConnections() {
        // 清除旧连接
        this.connections.forEach(connection => {
            this.scene.remove(connection);
        });
        this.connections = [];
        
        // 创建新连接
        for (let i = 0; i < this.elements.length - 1; i++) {
            this.createConnection(i, i + 1);
        }
    }
    
    rearrangeElements() {
        this.meshes.forEach((mesh, index) => {
            const newPosition = this.getElementPosition(index);
            
            gsap.to(mesh.position, {
                duration: 0.5,
                x: newPosition.x,
                y: newPosition.y,
                z: newPosition.z,
                ease: 'power2.out'
            });
        });
        
        // 延迟更新连接以确保位置动画完成
        setTimeout(() => {
            this.updateConnections();
        }, 500);
    }
    
    getElementPosition(index) {
        return {
            x: index * this.spacing - (this.elements.length - 1) * this.spacing / 2,
            y: 0,
            z: 0
        };
    }
    
    clear() {
        super.clear();
        
        // 清除连接
        this.connections.forEach(connection => {
            this.scene.remove(connection);
        });
        this.connections = [];
    }
    
    // 高亮节点
    highlightElement(index, color = 0xffff00) {
        if (index >= 0 && index < this.meshes.length) {
            const group = this.meshes[index];
            group.children.forEach(child => {
                if (child.material) {
                    child.material.color.setHex(color);
                    child.material.emissive.setHex(color * 0.1);
                }
            });
        }
    }
    
    // 重置节点颜色
    resetElementColor(index) {
        if (index >= 0 && index < this.meshes.length) {
            const group = this.meshes[index];
            group.children.forEach((child, childIndex) => {
                if (child.material) {
                    const defaultColor = childIndex === 0 ? 0x64b3f4 : 0x4a90e2;
                    child.material.color.setHex(defaultColor);
                    child.material.emissive.setHex(0x000000);
                }
            });
        }
    }
    
    resetAllColors() {
        this.meshes.forEach((_, index) => {
            this.resetElementColor(index);
        });
    }
}
